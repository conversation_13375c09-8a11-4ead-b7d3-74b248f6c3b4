import os


def get_project_root():
    script_dir = os.getcwd()
    current_dir = script_dir

    root_identifiers = {".git", "requirements.txt", "setup.py"}

    while current_dir != os.path.dirname(
        current_dir
    ):  # 当已经到达文件系统的根目录时停止
        if any(
            os.path.exists(os.path.join(current_dir, identifier))
            for identifier in root_identifiers
        ):
            return current_dir
        current_dir = os.path.dirname(current_dir)

    raise FileNotFoundError("项目根目录未找到")
