{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import re\n", "import shutil\n", "\n", "sys.path.append(\"..\")\n", "import experiment_utils\n", "\n", "os.chdir(experiment_utils.get_project_root())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Traverse all subdirectories within \"simulator_output\"\n", "for root, dirs, files in os.walk(\"simulator_output\"):\n", "    # Filter directories that start with a date pattern\n", "    date_dirs = [d for d in dirs if re.match(r\"\\d{4}-\\d{2}-\\d{2}\", d)]\n", "\n", "    # Sort directories by date\n", "    date_dirs.sort()\n", "\n", "    # Keep the latest directory\n", "    latest_dir = date_dirs[-1] if date_dirs else None\n", "\n", "    # Delete other directories\n", "    for d in date_dirs[:-1]:\n", "        dir_path = os.path.join(root, d)\n", "        if os.path.isdir(dir_path):\n", "            shutil.rmtree(dir_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# triangle 的 7b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-7b-hf  \\\n", "--cluster_config_num_replicas 4 \\\n", "--replica_config_tensor_parallel_size 1 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 1280  \\\n", "--poisson_request_interval_generator_config_qps 8 \\\n", "--metrics_config_store_token_completion_metrics \\\n", "--replica_scheduler_config_type triangle \\\n", "--global_scheduler_config_type triangle "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 10-12 09:46:45 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 10-12 09:46:48 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 10-12 09:46:55 simulator.py:80] Simulation ended at: 250.82750072615357s\n", "INFO 10-12 09:46:55 simulator.py:83] Writing output\n", "^C\n", "Exception ignored in atexit callback: <bound method Simulator._write_output of <vidur.simulator.Simulator object at 0x7075f45f4eb0>>\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/Playground/customized_vidur/vidur/simulator.py\", line 85, in _write_output\n", "    self._metric_store.plot()\n", "  File \"/home/<USER>/Playground/customized_vidur/vidur/metrics/metrics_store.py\", line 34, in wrapper\n", "    return func(self, *args, **kwargs)\n", "  File \"/home/<USER>/Playground/customized_vidur/vidur/metrics/metrics_store.py\", line 481, in plot\n", "    self._store_request_metrics(dir_plot_path)\n", "  File \"/home/<USER>/Playground/customized_vidur/vidur/metrics/metrics_store.py\", line 388, in _store_request_metrics\n", "    dataseries.plot_cdf(base_plot_path, dataseries._y_name, TIME_STR)\n", "  File \"/home/<USER>/Playground/customized_vidur/vidur/metrics/data_series.py\", line 255, in plot_cdf\n", "    fig.write_image(f\"{path}/{plot_name}.png\")\n", "  File \"/home/<USER>/Playground/customized_vidur/env/lib/python3.10/site-packages/plotly/basedatatypes.py\", line 3835, in write_image\n", "    return pio.write_image(self, *args, **kwargs)\n", "  File \"/home/<USER>/Playground/customized_vidur/env/lib/python3.10/site-packages/plotly/io/_kaleido.py\", line 266, in write_image\n", "    img_data = to_image(\n", "  File \"/home/<USER>/Playground/customized_vidur/env/lib/python3.10/site-packages/plotly/io/_kaleido.py\", line 143, in to_image\n", "    img_bytes = scope.transform(\n", "  File \"/home/<USER>/Playground/customized_vidur/env/lib/python3.10/site-packages/kaleido/scopes/plotly.py\", line 153, in transform\n", "    response = self._perform_transform(\n", "  File \"/home/<USER>/Playground/customized_vidur/env/lib/python3.10/site-packages/kaleido/scopes/base.py\", line 308, in _perform_transform\n", "    response = self._proc.stdout.readline()\n", "KeyboardInterrupt: \n"]}], "source": ["# triangle 的 70b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-70b-hf  \\\n", "--cluster_config_num_replicas 8 \\\n", "--replica_config_tensor_parallel_size 4 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 1280  \\\n", "--poisson_request_interval_generator_config_qps 13 \\\n", "--metrics_config_store_token_completion_metrics \\\n", "--replica_scheduler_config_type triangle \\\n", "--global_scheduler_config_type triangle \n", "\n", "#--triangle_scheduler_config_batch_size_cap 256 \\\n", "#--random_forrest_execution_time_predictor_config_prediction_max_batch_size 256"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 10-31 10:54:18 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 10-31 10:54:22 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 4}) and 1280 requests\n", "INFO 10-31 10:54:30 simulator.py:80] Simulation ended at: 215.32473817904753s\n", "INFO 10-31 10:54:30 simulator.py:83] Writing output\n", "INFO 10-31 10:55:33 simulator.py:86] Metrics written\n", "INFO 10-31 10:55:33 simulator.py:95] Chrome event trace written\n"]}], "source": ["# Disaggregation 的 70b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-7b-hf  \\\n", "--cluster_config_num_replicas 4 \\\n", "--replica_config_tensor_parallel_size 1 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 1280  \\\n", "--poisson_request_interval_generator_config_qps 10 \\\n", "--metrics_config_store_token_completion_metrics \\\n", "--replica_scheduler_config_type disaggregation \\\n", "--global_scheduler_config_type disaggregation \\\n", "--disaggregation_global_scheduler_config_replica_split_ratio 0.5 \n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hybrid PD 分离的 70b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-70b-hf  \\\n", "--cluster_config_num_replicas 4 \\\n", "--replica_config_tensor_parallel_size 4 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 128  \\\n", "--metrics_config_store_token_completion_metrics \\\n", "--replica_scheduler_config_type hybrid \\\n", "--global_scheduler_config_type hybrid \\\n", "--hybrid_global_scheduler_config_replica_split_ratio 0.5 \\\n", "--hybrid_global_scheduler_config_replica_mixed_prefill_factor 0.7 \\\n", "--hybrid_global_scheduler_config_replica_mixed_decode_factor 0.0 \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# vllm 的 70b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main  \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-70b-hf  \\\n", "--cluster_config_num_replicas 4 \\\n", "--replica_config_tensor_parallel_size 4 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 128  \\\n", "--replica_scheduler_config_type vllm  \n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 04-01 18:49:43 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-01 18:49:45 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 4}) and 1280 requests\n", "INFO 04-01 18:49:54 simulator.py:80] Simulation ended at: 412.68075804678574s\n", "INFO 04-01 18:49:54 simulator.py:83] Writing output\n", "INFO 04-01 18:50:19 simulator.py:86] Metrics written\n", "INFO 04-01 18:50:20 simulator.py:95] Chrome event trace written\n"]}], "source": ["# sarathi 的 70b 模拟\n", "\n", "!mamba run -p ./env --no-capture-output \\\n", "python3 -m vidur.main  \\\n", "--replica_config_device a100 \\\n", "--replica_config_network_device a100_dgx  \\\n", "--replica_config_model_name meta-llama/Llama-2-70b-hf  \\\n", "--cluster_config_num_replicas 4 \\\n", "--replica_config_tensor_parallel_size 4 \\\n", "--replica_config_num_pipeline_stages 1 \\\n", "--request_generator_config_type synthetic \\\n", "--length_generator_config_type trace \\\n", "--interval_generator_config_type poisson \\\n", "--trace_request_length_generator_config_max_tokens 4096 \\\n", "--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv \\\n", "--synthetic_request_generator_config_num_requests 1280  \\\n", "--poisson_request_interval_generator_config_qps 10 \\\n", "--replica_scheduler_config_type sarathi  \\\n", "--sarathi_scheduler_config_batch_size_cap 256 \\\n", "--sarathi_scheduler_config_chunk_size 256\n", "\n", "# --replica_config_network_device a100_dgx  \\"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# vllm 的 7b 模拟 debug\n", "\n", "!mamba run -p ./env --no-capture-output python3 -m vidur.main --replica_config_device a100 --cluster_config_num_replicas 4 --replica_config_tensor_parallel_size 2 --replica_config_num_pipeline_stages 2 --request_generator_config_type synthetic --length_generator_config_type trace --interval_generator_config_type poisson --trace_request_length_generator_config_max_tokens 4096  --trace_request_length_generator_config_decode_scale_factor 40 --trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv --synthetic_request_generator_config_num_requests 128 --replica_config_model_name meta-llama/Llama-2-7b-hf --replica_scheduler_config_type vllm --poisson_request_interval_generator_config_qps 100 \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vidur 重要参数及可选项\n", "\n", "--replica_scheduler_config_type [vllm|sarathi]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vidur 支持的参数 \n", "\n", "usage: main.py [-h] [--seed SEED] [--log_level LOG_LEVEL]\n", "               [--time_limit TIME_LIMIT]\n", "               [--cluster_config_num_replicas CLUSTER_CONFIG_NUM_REPLICAS]\n", "               [--replica_config_model_name REPLICA_CONFIG_MODEL_NAME]\n", "               [--replica_config_gpu_memory_utilization REPLICA_CONFIG_GPU_MEMORY_UTILIZATION]\n", "               [--replica_config_memory_margin_fraction REPLICA_CONFIG_MEMORY_MARGIN_FRACTION]\n", "               [--replica_config_num_pipeline_stages REPLICA_CONFIG_NUM_PIPELINE_STAGES]\n", "               [--replica_config_tensor_parallel_size REPLICA_CONFIG_TENSOR_PARALLEL_SIZE]\n", "               [--replica_config_device REPLICA_CONFIG_DEVICE]\n", "               [--replica_config_network_device REPLICA_CONFIG_NETWORK_DEVICE]\n", "               [--global_scheduler_config_type GLOBAL_SCHEDULER_CONFIG_TYPE]\n", "               [--replica_scheduler_config_type REPLICA_SCHEDULER_CONFIG_TYPE]\n", "               [--vllm_scheduler_config_max_num_seqs VLLM_SCHEDULER_CONFIG_MAX_NUM_SEQS]\n", "               [--vllm_scheduler_config_watermark_blocks_fraction VLLM_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION]\n", "               [--vllm_scheduler_config_block_size VLLM_SCHEDULER_CONFIG_BLOCK_SIZE]\n", "               [--vllm_scheduler_config_num_blocks VLLM_SCHEDULER_CONFIG_NUM_BLOCKS]\n", "               [--vllm_scheduler_config_batch_size_cap VLLM_SCHEDULER_CONFIG_BATCH_SIZE_CAP]\n", "               [--vllm_scheduler_config_max_batched_tokens VLLM_SCHEDULER_CONFIG_MAX_BATCHED_TOKENS]\n", "               [--vllm_scheduler_config_max_tokens_in_batch VLLM_SCHEDULER_CONFIG_MAX_TOKENS_IN_BATCH]\n", "               [--lightllm_scheduler_config_max_num_seqs LIGHTLLM_SCHEDULER_CONFIG_MAX_NUM_SEQS]\n", "               [--lightllm_scheduler_config_watermark_blocks_fraction LIGHTLLM_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION]\n", "               [--lightllm_scheduler_config_block_size LIGHTLLM_SCHEDULER_CONFIG_BLOCK_SIZE]\n", "               [--lightllm_scheduler_config_num_blocks LIGHTLLM_SCHEDULER_CONFIG_NUM_BLOCKS]\n", "               [--lightllm_scheduler_config_batch_size_cap LIGHTLLM_SCHEDULER_CONFIG_BATCH_SIZE_CAP]\n", "               [--lightllm_scheduler_config_max_batched_tokens LIGHTLLM_SCHEDULER_CONFIG_MAX_BATCHED_TOKENS]\n", "               [--lightllm_scheduler_config_max_tokens_in_batch LIGHTLLM_SCHEDULER_CONFIG_MAX_TOKENS_IN_BATCH]\n", "               [--lightllm_scheduler_config_max_waiting_iters LIGHTLLM_SCHEDULER_CONFIG_MAX_WAITING_ITERS]\n", "               [--orca_scheduler_config_max_num_seqs ORCA_SCHEDULER_CONFIG_MAX_NUM_SEQS]\n", "               [--orca_scheduler_config_watermark_blocks_fraction ORCA_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION]\n", "               [--orca_scheduler_config_block_size ORCA_SCHEDULER_CONFIG_BLOCK_SIZE]\n", "               [--orca_scheduler_config_num_blocks ORCA_SCHEDULER_CONFIG_NUM_BLOCKS]\n", "               [--orca_scheduler_config_batch_size_cap ORCA_SCHEDULER_CONFIG_BATCH_SIZE_CAP]\n", "               [--faster_transformer_scheduler_config_max_num_seqs FASTER_TRANSFORMER_SCHEDULER_CONFIG_MAX_NUM_SEQS]\n", "               [--faster_transformer_scheduler_config_watermark_blocks_fraction FASTER_TRANSFORMER_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION]\n", "               [--faster_transformer_scheduler_config_block_size FASTER_TRANSFORMER_SCHEDULER_CONFIG_BLOCK_SIZE]\n", "               [--faster_transformer_scheduler_config_num_blocks FASTER_TRANSFORMER_SCHEDULER_CONFIG_NUM_BLOCKS]\n", "               [--faster_transformer_scheduler_config_batch_size_cap FASTER_TRANSFORMER_SCHEDULER_CONFIG_BATCH_SIZE_CAP]\n", "               [--sarathi_scheduler_config_max_num_seqs SARATHI_SCHEDULER_CONFIG_MAX_NUM_SEQS]\n", "               [--sarathi_scheduler_config_watermark_blocks_fraction SARATHI_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION]\n", "               [--sarathi_scheduler_config_block_size SARATHI_SCHEDULER_CONFIG_BLOCK_SIZE]\n", "               [--sarathi_scheduler_config_num_blocks SARATHI_SCHEDULER_CONFIG_NUM_BLOCKS]\n", "               [--sarathi_scheduler_config_batch_size_cap SARATHI_SCHEDULER_CONFIG_BATCH_SIZE_CAP]\n", "               [--sarathi_scheduler_config_chunk_size SARATHI_SCHEDULER_CONFIG_CHUNK_SIZE]\n", "               [--request_generator_config_type REQUEST_GENERATOR_CONFIG_TYPE]\n", "               [--synthetic_request_generator_config_seed SYNTHETIC_REQUEST_GENERATOR_CONFIG_SEED]\n", "               [--synthetic_request_generator_config_max_tokens SYNTHETIC_REQUEST_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--length_generator_config_type LENGTH_GENERATOR_CONFIG_TYPE]\n", "               [--trace_request_length_generator_config_seed TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_SEED]\n", "               [--trace_request_length_generator_config_max_tokens TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--trace_request_length_generator_config_trace_file TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_TRACE_FILE]\n", "               [--trace_request_length_generator_config_prefill_scale_factor TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_SCALE_FACTOR]\n", "               [--trace_request_length_generator_config_decode_scale_factor TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_DECODE_SCALE_FACTOR]\n", "               [--zipf_request_length_generator_config_seed ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_SEED]\n", "               [--zipf_request_length_generator_config_max_tokens ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--zipf_request_length_generator_config_theta ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_THETA]\n", "               [--zipf_request_length_generator_config_scramble | --no-zipf_request_length_generator_config_scramble]\n", "               [--zipf_request_length_generator_config_min_tokens ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_MIN_TOKENS]\n", "               [--zipf_request_length_generator_config_prefill_to_decode_ratio ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TO_DECODE_RATIO]\n", "               [--uniform_request_length_generator_config_seed UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_SEED]\n", "               [--uniform_request_length_generator_config_max_tokens UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--uniform_request_length_generator_config_min_tokens UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_MIN_TOKENS]\n", "               [--uniform_request_length_generator_config_prefill_to_decode_ratio UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TO_DECODE_RATIO]\n", "               [--fixed_request_length_generator_config_seed FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_SEED]\n", "               [--fixed_request_length_generator_config_max_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--fixed_request_length_generator_config_prefill_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TOKENS]\n", "               [--fixed_request_length_generator_config_decode_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_DECODE_TOKENS]\n", "               [--interval_generator_config_type INTERVAL_GENERATOR_CONFIG_TYPE]\n", "               [--trace_request_interval_generator_config_seed TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED]\n", "               [--trace_request_interval_generator_config_trace_file TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_TRACE_FILE]\n", "               [--trace_request_interval_generator_config_start_time TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_START_TIME]\n", "               [--trace_request_interval_generator_config_end_time TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_END_TIME]\n", "               [--trace_request_interval_generator_config_time_scale_factor TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_TIME_SCALE_FACTOR]\n", "               [--poisson_request_interval_generator_config_seed POISSON_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED]\n", "               [--poisson_request_interval_generator_config_qps POISSON_REQUEST_INTERVAL_GENERATOR_CONFIG_QPS]\n", "               [--gamma_request_interval_generator_config_seed GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED]\n", "               [--gamma_request_interval_generator_config_qps GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_QPS]\n", "               [--gamma_request_interval_generator_config_cv GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_CV]\n", "               [--static_request_interval_generator_config_seed STATIC_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED]\n", "               [--synthetic_request_generator_config_num_requests SYNTHETIC_REQUEST_GENERATOR_CONFIG_NUM_REQUESTS]\n", "               [--synthetic_request_generator_config_duration SYNTHETIC_REQUEST_GENERATOR_CONFIG_DURATION]\n", "               [--trace_request_generator_config_seed TRACE_REQUEST_GENERATOR_CONFIG_SEED]\n", "               [--trace_request_generator_config_max_tokens TRACE_REQUEST_GENERATOR_CONFIG_MAX_TOKENS]\n", "               [--trace_request_generator_config_trace_file TRACE_REQUEST_GENERATOR_CONFIG_TRACE_FILE]\n", "               [--trace_request_generator_config_date TRACE_REQUEST_GENERATOR_CONFIG_DATE]\n", "               [--trace_request_generator_config_prefill_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_PREFILL_SCALE_FACTOR]\n", "               [--trace_request_generator_config_decode_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_DECODE_SCALE_FACTOR]\n", "               [--trace_request_generator_config_time_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_TIME_SCALE_FACTOR]\n", "               [--execution_time_predictor_config_type EXECUTION_TIME_PREDICTOR_CONFIG_TYPE]\n", "               [--linear_regression_execution_time_predictor_config_compute_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_COMPUTE_INPUT_FILE]\n", "               [--linear_regression_execution_time_predictor_config_attention_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_INPUT_FILE]\n", "               [--linear_regression_execution_time_predictor_config_all_reduce_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ALL_REDUCE_INPUT_FILE]\n", "               [--linear_regression_execution_time_predictor_config_send_recv_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_SEND_RECV_INPUT_FILE]\n", "               [--linear_regression_execution_time_predictor_config_cpu_overhead_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_CPU_OVERHEAD_INPUT_FILE]\n", "               [--linear_regression_execution_time_predictor_config_k_fold_cv_splits LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_K_FOLD_CV_SPLITS]\n", "               [--linear_regression_execution_time_predictor_config_no_cache | --no-linear_regression_execution_time_predictor_config_no_cache]\n", "               [--linear_regression_execution_time_predictor_config_kv_cache_prediction_granularity LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_KV_CACHE_PREDICTION_GRANULARITY]\n", "               [--linear_regression_execution_time_predictor_config_prediction_max_prefill_chunk_size LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_PREFILL_CHUNK_SIZE]\n", "               [--linear_regression_execution_time_predictor_config_prediction_max_batch_size LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_BATCH_SIZE]\n", "               [--linear_regression_execution_time_predictor_config_prediction_max_tokens_per_request LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_TOKENS_PER_REQUEST]\n", "               [--linear_regression_execution_time_predictor_config_attention_decode_batching_overhead_fraction LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_DECODE_BATCHING_OVERHEAD_FRACTION]\n", "               [--linear_regression_execution_time_predictor_config_attention_prefill_batching_overhead_fraction LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_PREFILL_BATCHING_OVERHEAD_FRACTION]\n", "               [--linear_regression_execution_time_predictor_config_nccl_cpu_launch_overhead_ms LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_LAUNCH_OVERHEAD_MS]\n", "               [--linear_regression_execution_time_predictor_config_nccl_cpu_skew_overhead_per_device_ms LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_SKEW_OVERHEAD_PER_DEVICE_MS]\n", "               [--linear_regression_execution_time_predictor_config_num_training_job_threads LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_TRAINING_JOB_THREADS]\n", "               [--linear_regression_execution_time_predictor_config_skip_cpu_overhead_modeling | --no-linear_regression_execution_time_predictor_config_skip_cpu_overhead_modeling]\n", "               [--linear_regression_execution_time_predictor_config_polynomial_degree LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_DEGREE [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_DEGREE ...]]\n", "               [--linear_regression_execution_time_predictor_config_polynomial_include_bias LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INCLUDE_BIAS [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INCLUDE_BIAS ...]]\n", "               [--linear_regression_execution_time_predictor_config_polynomial_interaction_only LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INTERACTION_ONLY [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INTERACTION_ONLY ...]]\n", "               [--linear_regression_execution_time_predictor_config_fit_intercept LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_FIT_INTERCEPT [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_FIT_INTERCEPT ...]]\n", "               [--random_forrest_execution_time_predictor_config_compute_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_COMPUTE_INPUT_FILE]\n", "               [--random_forrest_execution_time_predictor_config_attention_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_INPUT_FILE]\n", "               [--random_forrest_execution_time_predictor_config_all_reduce_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ALL_REDUCE_INPUT_FILE]\n", "               [--random_forrest_execution_time_predictor_config_send_recv_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_SEND_RECV_INPUT_FILE]\n", "               [--random_forrest_execution_time_predictor_config_cpu_overhead_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_CPU_OVERHEAD_INPUT_FILE]\n", "               [--random_forrest_execution_time_predictor_config_k_fold_cv_splits RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_K_FOLD_CV_SPLITS]\n", "               [--random_forrest_execution_time_predictor_config_no_cache | --no-random_forrest_execution_time_predictor_config_no_cache]\n", "               [--random_forrest_execution_time_predictor_config_kv_cache_prediction_granularity RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_KV_CACHE_PREDICTION_GRANULARITY]\n", "               [--random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_PREFILL_CHUNK_SIZE]\n", "               [--random_forrest_execution_time_predictor_config_prediction_max_batch_size RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_BATCH_SIZE]\n", "               [--random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_TOKENS_PER_REQUEST]\n", "               [--random_forrest_execution_time_predictor_config_attention_decode_batching_overhead_fraction RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_DECODE_BATCHING_OVERHEAD_FRACTION]\n", "               [--random_forrest_execution_time_predictor_config_attention_prefill_batching_overhead_fraction RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_PREFILL_BATCHING_OVERHEAD_FRACTION]\n", "               [--random_forrest_execution_time_predictor_config_nccl_cpu_launch_overhead_ms RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_LAUNCH_OVERHEAD_MS]\n", "               [--random_forrest_execution_time_predictor_config_nccl_cpu_skew_overhead_per_device_ms RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_SKEW_OVERHEAD_PER_DEVICE_MS]\n", "               [--random_forrest_execution_time_predictor_config_num_training_job_threads RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_TRAINING_JOB_THREADS]\n", "               [--random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling | --no-random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling]\n", "               [--random_forrest_execution_time_predictor_config_num_estimators RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_ESTIMATORS [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_ESTIMATORS ...]]\n", "               [--random_forrest_execution_time_predictor_config_max_depth RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MAX_DEPTH [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MAX_DEPTH ...]]\n", "               [--random_forrest_execution_time_predictor_config_min_samples_split RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MIN_SAMPLES_SPLIT [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MIN_SAMPLES_SPLIT ...]]\n", "               [--metrics_config_write_metrics | --no-metrics_config_write_metrics]\n", "               [--metrics_config_write_json_trace | --no-metrics_config_write_json_trace]\n", "               [--metrics_config_wandb_project METRICS_CONFIG_WANDB_PROJECT]\n", "               [--metrics_config_wandb_group METRICS_CONFIG_WANDB_GROUP]\n", "               [--metrics_config_wandb_run_name METRICS_CONFIG_WANDB_RUN_NAME]\n", "               [--metrics_config_wandb_sweep_id METRICS_CONFIG_WANDB_SWEEP_ID]\n", "               [--metrics_config_wandb_run_id METRICS_CONFIG_WANDB_RUN_ID]\n", "               [--metrics_config_enable_chrome_trace | --no-metrics_config_enable_chrome_trace]\n", "               [--metrics_config_save_table_to_wandb | --no-metrics_config_save_table_to_wandb]\n", "               [--metrics_config_store_plots | --no-metrics_config_store_plots]\n", "               [--metrics_config_store_operation_metrics | --no-metrics_config_store_operation_metrics]\n", "               [--metrics_config_store_token_completion_metrics | --no-metrics_config_store_token_completion_metrics]\n", "               [--metrics_config_store_request_metrics | --no-metrics_config_store_request_metrics]\n", "               [--metrics_config_store_batch_metrics | --no-metrics_config_store_batch_metrics]\n", "               [--metrics_config_store_utilization_metrics | --no-metrics_config_store_utilization_metrics]\n", "               [--metrics_config_keep_individual_batch_metrics | --no-metrics_config_keep_individual_batch_metrics]\n", "               [--metrics_config_subsamples METRICS_CONFIG_SUBSAMPLES]\n", "               [--metrics_config_min_batch_index METRICS_CONFIG_MIN_BATCH_INDEX]\n", "               [--metrics_config_max_batch_index METRICS_CONFIG_MAX_BATCH_INDEX]\n", "               [--metrics_config_output_dir METRICS_CONFIG_OUTPUT_DIR]\n", "               [--metrics_config_cache_dir METRICS_CONFIG_CACHE_DIR]\n", "\n", "options:\n", "  -h, --help            show this help message and exit\n", "  --seed SEED           Seed for the random number generator. (default: 42)\n", "  --log_level LOG_LEVEL\n", "                        Logging level. (default: info)\n", "  --time_limit TIME_LIMIT\n", "                        Time limit for simulation in seconds. 0 means no\n", "                        limit. (default: 0)\n", "  --cluster_config_num_replicas CLUSTER_CONFIG_NUM_REPLICAS\n", "                        Number of replicas. (default: 1)\n", "  --replica_config_model_name REPLICA_CONFIG_MODEL_NAME\n", "                        Model name. (default: meta-llama/Llama-2-7b-hf)\n", "  --replica_config_gpu_memory_utilization REPLICA_CONFIG_GPU_MEMORY_UTILIZATION\n", "                        GPU memory utilization. (default: 0.8)\n", "  --replica_config_memory_margin_fraction REPLICA_CONFIG_MEMORY_MARGIN_FRACTION\n", "                        Memory margin fraction. (default: 0.1)\n", "  --replica_config_num_pipeline_stages REPLICA_CONFIG_NUM_PIPELINE_STAGES\n", "                        Number of pipeline stages. (default: 4)\n", "  --replica_config_tensor_parallel_size REPLICA_CONFIG_TENSOR_PARALLEL_SIZE\n", "                        Tensor parallel size. (default: 1)\n", "  --replica_config_device REPLICA_CONFIG_DEVICE\n", "                        Device. (default: a100)\n", "  --replica_config_network_device REPLICA_CONFIG_NETWORK_DEVICE\n", "                        Network device. (default: a100_pairwise_nvlink)\n", "  --global_scheduler_config_type GLOBAL_SCHEDULER_CONFIG_TYPE\n", "                        Global scheduler config. (default: round_robin)\n", "  --replica_scheduler_config_type REPLICA_SCHEDULER_CONFIG_TYPE\n", "                        Replica scheduler config. (default: sarathi)\n", "  --vllm_scheduler_config_max_num_seqs VLLM_SCHEDULER_CONFIG_MAX_NUM_SEQS\n", "                        Maximum number of sequences. (default: 128)\n", "  --vllm_scheduler_config_watermark_blocks_fraction VLLM_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION\n", "                        Watermark blocks fraction. (default: 0.01)\n", "  --vllm_scheduler_config_block_size VLLM_SCHEDULER_CONFIG_BLOCK_SIZE\n", "                        Block size. (default: 16)\n", "  --vllm_scheduler_config_num_blocks VLLM_SCHEDULER_CONFIG_NUM_BLOCKS\n", "                        Number of blocks. (default: None)\n", "  --vllm_scheduler_config_batch_size_cap VLLM_SCHEDULER_CONFIG_BATCH_SIZE_CAP\n", "                        Maximum batch size cap. (default: 128)\n", "  --vllm_scheduler_config_max_batched_tokens VLLM_SCHEDULER_CONFIG_MAX_BATCHED_TOKENS\n", "                        Maximum batched tokens for vLLM. (default: None)\n", "  --vllm_scheduler_config_max_tokens_in_batch VLLM_SCHEDULER_CONFIG_MAX_TOKENS_IN_BATCH\n", "                        Maximum tokens in batch for vLLM. (default: 4096)\n", "  --lightllm_scheduler_config_max_num_seqs LIGHTLLM_SCHEDULER_CONFIG_MAX_NUM_SEQS\n", "                        Maximum number of sequences. (default: 128)\n", "  --lightllm_scheduler_config_watermark_blocks_fraction LIGHTLLM_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION\n", "                        Watermark blocks fraction. (default: 0.01)\n", "  --lightllm_scheduler_config_block_size LIGHTLLM_SCHEDULER_CONFIG_BLOCK_SIZE\n", "                        Block size. (default: 16)\n", "  --lightllm_scheduler_config_num_blocks LIGHTLLM_SCHEDULER_CONFIG_NUM_BLOCKS\n", "                        Number of blocks. (default: None)\n", "  --lightllm_scheduler_config_batch_size_cap LIGHTLLM_SCHEDULER_CONFIG_BATCH_SIZE_CAP\n", "                        Maximum batch size cap. (default: 128)\n", "  --lightllm_scheduler_config_max_batched_tokens LIGHTLLM_SCHEDULER_CONFIG_MAX_BATCHED_TOKENS\n", "                        Maximum batched tokens for LightLLM. (default: None)\n", "  --lightllm_scheduler_config_max_tokens_in_batch LIGHTLLM_SCHEDULER_CONFIG_MAX_TOKENS_IN_BATCH\n", "                        Maximum tokens in batch for LightLLM. (default: 4096)\n", "  --lightllm_scheduler_config_max_waiting_iters LIGHTLLM_SCHEDULER_CONFIG_MAX_WAITING_ITERS\n", "                        Maximum waiting iterations for LightLLM. (default: 10)\n", "  --orca_scheduler_config_max_num_seqs ORCA_SCHEDULER_CONFIG_MAX_NUM_SEQS\n", "                        Maximum number of sequences. (default: 128)\n", "  --orca_scheduler_config_watermark_blocks_fraction ORCA_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION\n", "                        Watermark blocks fraction. (default: 0.01)\n", "  --orca_scheduler_config_block_size ORCA_SCHEDULER_CONFIG_BLOCK_SIZE\n", "                        Block size. (default: 16)\n", "  --orca_scheduler_config_num_blocks ORCA_SCHEDULER_CONFIG_NUM_BLOCKS\n", "                        Number of blocks. (default: None)\n", "  --orca_scheduler_config_batch_size_cap ORCA_SCHEDULER_CONFIG_BATCH_SIZE_CAP\n", "                        Maximum batch size cap. (default: 128)\n", "  --faster_transformer_scheduler_config_max_num_seqs FASTER_TRANSFORMER_SCHEDULER_CONFIG_MAX_NUM_SEQS\n", "                        Maximum number of sequences. (default: 128)\n", "  --faster_transformer_scheduler_config_watermark_blocks_fraction FASTER_TRANSFORMER_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION\n", "                        Watermark blocks fraction. (default: 0.01)\n", "  --faster_transformer_scheduler_config_block_size FASTER_TRANSFORMER_SCHEDULER_CONFIG_BLOCK_SIZE\n", "                        Block size. (default: 16)\n", "  --faster_transformer_scheduler_config_num_blocks FASTER_TRANSFORMER_SCHEDULER_CONFIG_NUM_BLOCKS\n", "                        Number of blocks. (default: None)\n", "  --faster_transformer_scheduler_config_batch_size_cap FASTER_TRANSFORMER_SCHEDULER_CONFIG_BATCH_SIZE_CAP\n", "                        Maximum batch size cap. (default: 128)\n", "  --sarathi_scheduler_config_max_num_seqs SARATHI_SCHEDULER_CONFIG_MAX_NUM_SEQS\n", "                        Maximum number of sequences. (default: 128)\n", "  --sarathi_scheduler_config_watermark_blocks_fraction SARATHI_SCHEDULER_CONFIG_WATERMARK_BLOCKS_FRACTION\n", "                        Watermark blocks fraction. (default: 0.01)\n", "  --sarathi_scheduler_config_block_size SARATHI_SCHEDULER_CONFIG_BLOCK_SIZE\n", "                        Block size. (default: 16)\n", "  --sarathi_scheduler_config_num_blocks SARATHI_SCHEDULER_CONFIG_NUM_BLOCKS\n", "                        Number of blocks. (default: None)\n", "  --sarathi_scheduler_config_batch_size_cap SARATHI_SCHEDULER_CONFIG_BATCH_SIZE_CAP\n", "                        Maximum batch size cap. (default: 128)\n", "  --sarathi_scheduler_config_chunk_size SARATHI_SCHEDULER_CONFIG_CHUNK_SIZE\n", "                        Chunk size for <PERSON><PERSON>. (default: 512)\n", "  --request_generator_config_type REQUEST_GENERATOR_CONFIG_TYPE\n", "                        Request generator config. (default: synthetic)\n", "  --synthetic_request_generator_config_seed SYNTHETIC_REQUEST_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --synthetic_request_generator_config_max_tokens SYNTHETIC_REQUEST_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens. (default: 4096)\n", "  --length_generator_config_type LENGTH_GENERATOR_CONFIG_TYPE\n", "                        Length generator config for Synthetic Request\n", "                        Generator. (default: fixed)\n", "  --trace_request_length_generator_config_seed TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --trace_request_length_generator_config_max_tokens TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens for the trace request length generator.\n", "                        (default: 4096)\n", "  --trace_request_length_generator_config_trace_file TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_TRACE_FILE\n", "                        Path to the trace request length generator file.\n", "                        (default: data/processed_traces/sharegpt_8k_filtered_s\n", "                        tats_llama2_tokenizer.csv)\n", "  --trace_request_length_generator_config_prefill_scale_factor TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_SCALE_FACTOR\n", "                        Prefill scale factor for the trace request length\n", "                        generator. (default: 1)\n", "  --trace_request_length_generator_config_decode_scale_factor TRACE_REQUEST_LENGTH_GENERATOR_CONFIG_DECODE_SCALE_FACTOR\n", "                        Decode scale factor for the trace request length\n", "                        generator. (default: 1)\n", "  --zipf_request_length_generator_config_seed ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --zipf_request_length_generator_config_max_tokens ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens for Zipf Request Length Generator.\n", "                        (default: 4096)\n", "  --zipf_request_length_generator_config_theta ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_THETA\n", "                        Theta for Zipf Request Length Generator. (default:\n", "                        0.6)\n", "  --zipf_request_length_generator_config_scramble, --no-zipf_request_length_generator_config_scramble\n", "                        Scramble for Zipf Request Length Generator. (default:\n", "                        False)\n", "  --zipf_request_length_generator_config_min_tokens ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_MIN_TOKENS\n", "                        Minimum tokens for Zipf Request Length Generator.\n", "                        (default: 1024)\n", "  --zipf_request_length_generator_config_prefill_to_decode_ratio ZIPF_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TO_DECODE_RATIO\n", "                        Prefill to decode ratio for Zipf Request Length\n", "                        Generator. (default: 20.0)\n", "  --uniform_request_length_generator_config_seed UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --uniform_request_length_generator_config_max_tokens UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens for Uniform Request Length Generator.\n", "                        (default: 4096)\n", "  --uniform_request_length_generator_config_min_tokens UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_MIN_TOKENS\n", "                        Minimum tokens for Uniform Request Length Generator.\n", "                        (default: 1024)\n", "  --uniform_request_length_generator_config_prefill_to_decode_ratio UNIFORM_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TO_DECODE_RATIO\n", "                        Prefill to decode ratio for Uniform Request Length\n", "                        Generator. (default: 20.0)\n", "  --fixed_request_length_generator_config_seed FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --fixed_request_length_generator_config_max_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens. (default: 4096)\n", "  --fixed_request_length_generator_config_prefill_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_PREFILL_TOKENS\n", "                        Prefill tokens for Fixed Request Length Generator.\n", "                        (default: 2048)\n", "  --fixed_request_length_generator_config_decode_tokens FIXED_REQUEST_LENGTH_GENERATOR_CONFIG_DECODE_TOKENS\n", "                        Decode tokens for Fixed Request Length Generator.\n", "                        (default: 512)\n", "  --interval_generator_config_type INTERVAL_GENERATOR_CONFIG_TYPE\n", "                        Interval generator config for Synthetic Request\n", "                        Generator. (default: poisson)\n", "  --trace_request_interval_generator_config_seed TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --trace_request_interval_generator_config_trace_file TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_TRACE_FILE\n", "                        Path to the trace request interval generator file.\n", "                        (default: data/processed_traces/AzureFunctionsInvocati\n", "                        onTraceForTwoWeeksJan2021Processed.csv)\n", "  --trace_request_interval_generator_config_start_time TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_START_TIME\n", "                        Start time of the trace request interval generator.\n", "                        (default: 1970-01-04 12:00:00)\n", "  --trace_request_interval_generator_config_end_time TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_END_TIME\n", "                        End time of the trace request interval generator.\n", "                        (default: 1970-01-04 15:00:00)\n", "  --trace_request_interval_generator_config_time_scale_factor TRACE_REQUEST_INTERVAL_GENERATOR_CONFIG_TIME_SCALE_FACTOR\n", "                        Time scale factor for the trace request interval\n", "                        generator. (default: 0.3)\n", "  --poisson_request_interval_generator_config_seed POISSON_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --poisson_request_interval_generator_config_qps POISSON_REQUEST_INTERVAL_GENERATOR_CONFIG_QPS\n", "                        Queries per second for Poisson Request Interval\n", "                        Generator. (default: 0.5)\n", "  --gamma_request_interval_generator_config_seed GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --gamma_request_interval_generator_config_qps GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_QPS\n", "                        Queries per second for Gamma Request Interval\n", "                        Generator. (default: 0.2)\n", "  --gamma_request_interval_generator_config_cv GAMMA_REQUEST_INTERVAL_GENERATOR_CONFIG_CV\n", "                        Coefficient of variation for Gamma Request Interval\n", "                        Generator. (default: 0.5)\n", "  --static_request_interval_generator_config_seed STATIC_REQUEST_INTERVAL_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --synthetic_request_generator_config_num_requests SYNTHETIC_REQUEST_GENERATOR_CONFIG_NUM_REQUESTS\n", "                        Number of requests for Synthetic Request Generator.\n", "                        (default: 128)\n", "  --synthetic_request_generator_config_duration SYNTHETIC_REQUEST_GENERATOR_CONFIG_DURATION\n", "                        Duration of the synthetic request generator. (default:\n", "                        None)\n", "  --trace_request_generator_config_seed TRACE_REQUEST_GENERATOR_CONFIG_SEED\n", "                        Seed for the random number generator. (default: 42)\n", "  --trace_request_generator_config_max_tokens TRACE_REQUEST_GENERATOR_CONFIG_MAX_TOKENS\n", "                        Maximum tokens for the trace request generator.\n", "                        (default: 4096)\n", "  --trace_request_generator_config_trace_file TRACE_REQUEST_GENERATOR_CONFIG_TRACE_FILE\n", "                        Path to the trace request generator file. (default:\n", "                        data/processed_traces/sydney_enterprise.csv)\n", "  --trace_request_generator_config_date TRACE_REQUEST_GENERATOR_CONFIG_DATE\n", "                        Date for the trace request generator. (default:\n", "                        2023-08-21)\n", "  --trace_request_generator_config_prefill_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_PREFILL_SCALE_FACTOR\n", "                        Prefill scale factor for the trace request generator.\n", "                        (default: 0.3)\n", "  --trace_request_generator_config_decode_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_DECODE_SCALE_FACTOR\n", "                        Decode scale factor for the trace request generator.\n", "                        (default: 1)\n", "  --trace_request_generator_config_time_scale_factor TRACE_REQUEST_GENERATOR_CONFIG_TIME_SCALE_FACTOR\n", "                        Time scale factor for the trace request generator.\n", "                        (default: 0.04)\n", "  --execution_time_predictor_config_type EXECUTION_TIME_PREDICTOR_CONFIG_TYPE\n", "                        Execution time predictor config. (default:\n", "                        random_forrest)\n", "  --linear_regression_execution_time_predictor_config_compute_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_COMPUTE_INPUT_FILE\n", "                        Path to the compute input file. (default:\n", "                        ./data/profiling/compute/{DEVICE}/{MODEL}/mlp.csv)\n", "  --linear_regression_execution_time_predictor_config_attention_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_INPUT_FILE\n", "                        Path to the attention input file. (default: ./data/pro\n", "                        filing/compute/{DEVICE}/{MODEL}/attention.csv)\n", "  --linear_regression_execution_time_predictor_config_all_reduce_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ALL_REDUCE_INPUT_FILE\n", "                        Path to the all reduce input file. (default: ./data/pr\n", "                        ofiling/network/{NETWORK_DEVICE}/all_reduce.csv)\n", "  --linear_regression_execution_time_predictor_config_send_recv_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_SEND_RECV_INPUT_FILE\n", "                        Path to the send recv input file. (default: ./data/pro\n", "                        filing/network/{NETWORK_DEVICE}/send_recv.csv)\n", "  --linear_regression_execution_time_predictor_config_cpu_overhead_input_file LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_CPU_OVERHEAD_INPUT_FILE\n", "                        Path to the cpu overhead input file. (default: ./data/\n", "                        profiling/cpu_overhead/{NETWORK_DEVICE}/{MODEL}/cpu_ov\n", "                        erheads.csv)\n", "  --linear_regression_execution_time_predictor_config_k_fold_cv_splits LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_K_FOLD_CV_SPLITS\n", "                        Number of k fold cross validation splits. (default:\n", "                        10)\n", "  --linear_regression_execution_time_predictor_config_no_cache, --no-linear_regression_execution_time_predictor_config_no_cache\n", "                        Whether to cache prediction models. (default: False)\n", "  --linear_regression_execution_time_predictor_config_kv_cache_prediction_granularity LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_KV_CACHE_PREDICTION_GRANULARITY\n", "                        KV cache prediction granularity. (default: 64)\n", "  --linear_regression_execution_time_predictor_config_prediction_max_prefill_chunk_size LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_PREFILL_CHUNK_SIZE\n", "                        Max prefill chunk size for prediction. (default: 4096)\n", "  --linear_regression_execution_time_predictor_config_prediction_max_batch_size LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_BATCH_SIZE\n", "                        Max batch size for prediction. (default: 128)\n", "  --linear_regression_execution_time_predictor_config_prediction_max_tokens_per_request LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_TOKENS_PER_REQUEST\n", "                        Max tokens per request for prediction. (default: 4096)\n", "  --linear_regression_execution_time_predictor_config_attention_decode_batching_overhead_fraction LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_DECODE_BATCHING_OVERHEAD_FRACTION\n", "                        Attention decode batching overhead fraction. (default:\n", "                        0.1)\n", "  --linear_regression_execution_time_predictor_config_attention_prefill_batching_overhead_fraction LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_PREFILL_BATCHING_OVERHEAD_FRACTION\n", "                        Attention prefill batching overhead fraction.\n", "                        (default: 0.1)\n", "  --linear_regression_execution_time_predictor_config_nccl_cpu_launch_overhead_ms LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_LAUNCH_OVERHEAD_MS\n", "                        NCCL CPU launch overhead in ms. (default: 0.02)\n", "  --linear_regression_execution_time_predictor_config_nccl_cpu_skew_overhead_per_device_ms LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_SKEW_OVERHEAD_PER_DEVICE_MS\n", "                        NCCL CPU skew overhead per device in ms. (default:\n", "                        0.0)\n", "  --linear_regression_execution_time_predictor_config_num_training_job_threads LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_TRAINING_JOB_THREADS\n", "                        Number of training job threads. (default: -1)\n", "  --linear_regression_execution_time_predictor_config_skip_cpu_overhead_modeling, --no-linear_regression_execution_time_predictor_config_skip_cpu_overhead_modeling\n", "                        Whether to skip CPU overhead modeling. (default: True)\n", "  --linear_regression_execution_time_predictor_config_polynomial_degree LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_DEGREE [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_DEGREE ...]\n", "                        Polynomial degree for linear regression. (default: [1,\n", "                        2, 3, 4, 5])\n", "  --linear_regression_execution_time_predictor_config_polynomial_include_bias LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INCLUDE_BIAS [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INCLUDE_BIAS ...]\n", "                        Polynomial include bias for linear regression.\n", "                        (default: [True, False])\n", "  --linear_regression_execution_time_predictor_config_polynomial_interaction_only LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INTERACTION_ONLY [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_POLYNOMIAL_INTERACTION_ONLY ...]\n", "                        Polynomial interaction only for linear regression.\n", "                        (default: [True, False])\n", "  --linear_regression_execution_time_predictor_config_fit_intercept LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_FIT_INTERCEPT [LINEAR_REGRESSION_EXECUTION_TIME_PREDICTOR_CONFIG_FIT_INTERCEPT ...]\n", "                        Fit intercept for linear regression. (default: [True,\n", "                        False])\n", "  --random_forrest_execution_time_predictor_config_compute_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_COMPUTE_INPUT_FILE\n", "                        Path to the compute input file. (default:\n", "                        ./data/profiling/compute/{DEVICE}/{MODEL}/mlp.csv)\n", "  --random_forrest_execution_time_predictor_config_attention_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_INPUT_FILE\n", "                        Path to the attention input file. (default: ./data/pro\n", "                        filing/compute/{DEVICE}/{MODEL}/attention.csv)\n", "  --random_forrest_execution_time_predictor_config_all_reduce_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ALL_REDUCE_INPUT_FILE\n", "                        Path to the all reduce input file. (default: ./data/pr\n", "                        ofiling/network/{NETWORK_DEVICE}/all_reduce.csv)\n", "  --random_forrest_execution_time_predictor_config_send_recv_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_SEND_RECV_INPUT_FILE\n", "                        Path to the send recv input file. (default: ./data/pro\n", "                        filing/network/{NETWORK_DEVICE}/send_recv.csv)\n", "  --random_forrest_execution_time_predictor_config_cpu_overhead_input_file RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_CPU_OVERHEAD_INPUT_FILE\n", "                        Path to the cpu overhead input file. (default: ./data/\n", "                        profiling/cpu_overhead/{NETWORK_DEVICE}/{MODEL}/cpu_ov\n", "                        erheads.csv)\n", "  --random_forrest_execution_time_predictor_config_k_fold_cv_splits RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_K_FOLD_CV_SPLITS\n", "                        Number of k fold cross validation splits. (default:\n", "                        10)\n", "  --random_forrest_execution_time_predictor_config_no_cache, --no-random_forrest_execution_time_predictor_config_no_cache\n", "                        Whether to cache prediction models. (default: False)\n", "  --random_forrest_execution_time_predictor_config_kv_cache_prediction_granularity RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_KV_CACHE_PREDICTION_GRANULARITY\n", "                        KV cache prediction granularity. (default: 64)\n", "  --random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_PREFILL_CHUNK_SIZE\n", "                        Max prefill chunk size for prediction. (default: 4096)\n", "  --random_forrest_execution_time_predictor_config_prediction_max_batch_size RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_BATCH_SIZE\n", "                        Max batch size for prediction. (default: 128)\n", "  --random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_PREDICTION_MAX_TOKENS_PER_REQUEST\n", "                        Max tokens per request for prediction. (default: 4096)\n", "  --random_forrest_execution_time_predictor_config_attention_decode_batching_overhead_fraction RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_DECODE_BATCHING_OVERHEAD_FRACTION\n", "                        Attention decode batching overhead fraction. (default:\n", "                        0.1)\n", "  --random_forrest_execution_time_predictor_config_attention_prefill_batching_overhead_fraction RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_ATTENTION_PREFILL_BATCHING_OVERHEAD_FRACTION\n", "                        Attention prefill batching overhead fraction.\n", "                        (default: 0.1)\n", "  --random_forrest_execution_time_predictor_config_nccl_cpu_launch_overhead_ms RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_LAUNCH_OVERHEAD_MS\n", "                        NCCL CPU launch overhead in ms. (default: 0.02)\n", "  --random_forrest_execution_time_predictor_config_nccl_cpu_skew_overhead_per_device_ms RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NCCL_CPU_SKEW_OVERHEAD_PER_DEVICE_MS\n", "                        NCCL CPU skew overhead per device in ms. (default:\n", "                        0.0)\n", "  --random_forrest_execution_time_predictor_config_num_training_job_threads RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_TRAINING_JOB_THREADS\n", "                        Number of training job threads. (default: -1)\n", "  --random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling, --no-random_forrest_execution_time_predictor_config_skip_cpu_overhead_modeling\n", "                        Whether to skip CPU overhead modeling. (default: True)\n", "  --random_forrest_execution_time_predictor_config_num_estimators RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_ESTIMATORS [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_NUM_ESTIMATORS ...]\n", "                        Number of estimators for random forest. (default:\n", "                        [250, 500, 750])\n", "  --random_forrest_execution_time_predictor_config_max_depth RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MAX_DEPTH [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MAX_DEPTH ...]\n", "                        Maximum depth for random forest. (default: [8, 16,\n", "                        32])\n", "  --random_forrest_execution_time_predictor_config_min_samples_split RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MIN_SAMPLES_SPLIT [RANDOM_FORREST_EXECUTION_TIME_PREDICTOR_CONFIG_MIN_SAMPLES_SPLIT ...]\n", "                        Minimum samples split for random forest. (default: [2,\n", "                        5, 10])\n", "  --metrics_config_write_metrics, --no-metrics_config_write_metrics\n", "                        Whether to write metrics. (default: True)\n", "  --metrics_config_write_json_trace, --no-metrics_config_write_json_trace\n", "                        Whether to write json trace. (default: False)\n", "  --metrics_config_wandb_project METRICS_CONFIG_WANDB_PROJECT\n", "                        Weights & Biases project name. (default: None)\n", "  --metrics_config_wandb_group METRICS_CONFIG_WANDB_GROUP\n", "                        Weights & Biases group name. (default: None)\n", "  --metrics_config_wandb_run_name METRICS_CONFIG_WANDB_RUN_NAME\n", "                        Weights & Biases run name. (default: None)\n", "  --metrics_config_wandb_sweep_id METRICS_CONFIG_WANDB_SWEEP_ID\n", "                        Weights & Biases sweep id. (default: None)\n", "  --metrics_config_wandb_run_id METRICS_CONFIG_WANDB_RUN_ID\n", "                        Weights & Biases run id. (default: None)\n", "  --metrics_config_enable_chrome_trace, --no-metrics_config_enable_chrome_trace\n", "                        Enable Chrome tracing. (default: True)\n", "  --metrics_config_save_table_to_wandb, --no-metrics_config_save_table_to_wandb\n", "                        Whether to save table to wandb. (default: False)\n", "  --metrics_config_store_plots, --no-metrics_config_store_plots\n", "                        Whether to store plots. (default: True)\n", "  --metrics_config_store_operation_metrics, --no-metrics_config_store_operation_metrics\n", "                        Whether to store operation metrics. (default: False)\n", "  --metrics_config_store_token_completion_metrics, --no-metrics_config_store_token_completion_metrics\n", "                        Whether to store token completion metrics. (default:\n", "                        False)\n", "  --metrics_config_store_request_metrics, --no-metrics_config_store_request_metrics\n", "                        Whether to store request metrics. (default: True)\n", "  --metrics_config_store_batch_metrics, --no-metrics_config_store_batch_metrics\n", "                        Whether to store batch metrics. (default: True)\n", "  --metrics_config_store_utilization_metrics, --no-metrics_config_store_utilization_metrics\n", "                        Whether to store utilization metrics. (default: True)\n", "  --metrics_config_keep_individual_batch_metrics, --no-metrics_config_keep_individual_batch_metrics\n", "                        Whether to keep individual batch metrics. (default:\n", "                        False)\n", "  --metrics_config_subsamples METRICS_CONFIG_SUBSAMPLES\n", "                        Subsamples. (default: None)\n", "  --metrics_config_min_batch_index METRICS_CONFIG_MIN_BATCH_INDEX\n", "                        Minimum batch index. (default: None)\n", "  --metrics_config_max_batch_index METRICS_CONFIG_MAX_BATCH_INDEX\n", "                        Maximum batch index. (default: None)\n", "  --metrics_config_output_dir METRICS_CONFIG_OUTPUT_DIR\n", "                        Output directory. (default: simulator_output)\n", "  --metrics_config_cache_dir METRICS_CONFIG_CACHE_DIR\n", "                        Cache directory. (default: cache)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'torch'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtime\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# 确保你在使用 GPU\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'torch'"]}], "source": ["import torch\n", "import time\n", "\n", "# 确保你在使用 GPU\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# 设置一个大张量的大小\n", "# 例如 100MB 的张量\n", "tensor_size = 100 * 1024 * 1024  # 以字节为单位\n", "data = torch.rand(tensor_size, dtype=torch.float32)  # 创建一个大的张量\n", "\n", "# 测量传输时间\n", "start_time = time.time()\n", "data_to_gpu = data.to(device)\n", "end_time = time.time()\n", "\n", "# 计算带宽\n", "transfer_time = end_time - start_time\n", "data_size_mb = (\n", "    data_to_gpu.element_size() * data_to_gpu.nelement() / (1024 * 1024)\n", ")  # 转换为 MB\n", "bandwidth = data_size_mb / transfer_time  # 单位是 MB/s\n", "\n", "print(f\"H2D 带宽: {bandwidth:.2f} MB/s\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}