#!/usr/bin/env python3
"""
Example script demonstrating how to use the new multi-SLO satisfaction rate analysis functionality.

This script shows how to:
1. Load configuration with multiple SLO groups
2. Calculate satisfaction rates for all SLO groups
3. Display formatted results in terminal
4. Use the results for further analysis

Usage:
    python example_multi_slo_analysis.py
"""

import sys
import os

# Add the parent directories to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from simulation_utils import load_evaluation_conf, PlotterBase

class SLOAnalyzer(PlotterBase):
    """Example analyzer that extends PlotterBase to demonstrate multi-SLO analysis"""
    
    def plot(self):
        """Required by PlotterBase, but not used in this example"""
        pass
    
    def run_comprehensive_slo_analysis(self):
        """Run a comprehensive SLO analysis for all configured schedulers"""
        print("Starting comprehensive SLO analysis...")
        
        # Get all configured schedulers for analysis
        selected_schedulers = self.conf.selected_run_schedulers
        print(f"Analyzing {len(selected_schedulers)} schedulers: {', '.join(selected_schedulers)}")
        
        # Note: This would require actual simulation data files to work
        # For demonstration, we'll show how to use the methods
        try:
            # This is the main method that calculates and displays everything
            results = self.analyze_and_display_slo_satisfaction_rates(selected_schedulers)
            
            # You can also access individual components:
            # results = self._fetch_all_latency_qps_results_with_all_slo_groups(selected_schedulers)
            # self.display_satisfaction_rates_summary(results, self.conf.selected_workload)
            
            return results
            
        except FileNotFoundError as e:
            print(f"⚠️  Simulation data not found: {e}")
            print("   This is expected if you haven't run the simulations yet.")
            print("   The methods are ready to use once you have simulation data.")
            return None
    
    def demonstrate_slo_group_access(self):
        """Demonstrate how to access SLO group information"""
        workload = self.workload
        
        print(f"\nWorkload: {self.conf.selected_workload}")
        print("Available SLO groups:")
        
        slo_groups = workload.get_all_slo_groups()
        for group_name, (ttft_slo, tpot_slo) in slo_groups.items():
            print(f"  {group_name}:")
            print(f"    TTFT SLO: {ttft_slo:.3f} seconds")
            print(f"    TPOT SLO: {tpot_slo:.3f} seconds")
        
        # Legacy access still works
        print(f"\nLegacy access (first SLO group):")
        print(f"  TTFT SLO: {workload.get_ttft_slo():.3f} seconds")
        print(f"  TPOT SLO: {workload.get_tpot_slo():.3f} seconds")

def main():
    """Main function demonstrating the multi-SLO analysis functionality"""
    print("="*70)
    print("MULTI-SLO SATISFACTION RATE ANALYSIS EXAMPLE")
    print("="*70)
    
    try:
        # Load configuration
        conf = load_evaluation_conf('conf.yaml')
        print(f"✓ Loaded configuration for workload: {conf.selected_workload}")
        
        # Create analyzer
        analyzer = SLOAnalyzer(conf)
        
        # Demonstrate SLO group access
        analyzer.demonstrate_slo_group_access()
        
        # Run comprehensive analysis
        print("\n" + "="*70)
        results = analyzer.run_comprehensive_slo_analysis()
        
        if results:
            print("\n✅ Analysis completed successfully!")
            print("\nYou can now use the results for further processing:")
            print("- results contains satisfaction rates for all SLO groups")
            print("- Each result tuple contains: (scheduler, ttfts, tpots, legacy_rate, all_slo_rates)")
        else:
            print("\n⚠️  Analysis requires simulation data files.")
            print("Run the evaluation simulations first to generate the required data.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
