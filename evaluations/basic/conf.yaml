### 执行总开关
# run: True
run: False


### 配置选择
selected_workload: "llama2_70b"

selected_run_schedulers: 
  - "p7d1"
  - "p6d2"
  - "p5d3"
  - "p4d4"
  - "vllm"
  - "cp1024"
  - "cp512"
  - "cp256"
  - "cp128"
  - "triangle"


### 可用调度器
schedulers: # Dict[str, SchedulerConf]
  p7d1:
    name: "p7d1"
    arguments: 
      "global_scheduler_config_type": "disaggregation"
      "replica_scheduler_config_type": "disaggregation"
      "disaggregation_global_scheduler_config_replica_split_ratio": 0.875
  p6d2:
    name: "p6d2"
    arguments: 
      "global_scheduler_config_type": "disaggregation"
      "replica_scheduler_config_type": "disaggregation"
      "disaggregation_global_scheduler_config_replica_split_ratio": 0.75
  p5d3:
    name: "p5d3"
    arguments: 
      "global_scheduler_config_type": "disaggregation"
      "replica_scheduler_config_type": "disaggregation"
      "disaggregation_global_scheduler_config_replica_split_ratio": 0.625
  p4d4:
    name: "p4d4"
    arguments: 
      "global_scheduler_config_type": "disaggregation"
      "replica_scheduler_config_type": "disaggregation"
      "disaggregation_global_scheduler_config_replica_split_ratio": 0.5
  vllm:
    name: "vllm"
    arguments:
      "replica_scheduler_config_type": "vllm"
  cp1024:
    name: "cp1024"
    arguments:
      "replica_scheduler_config_type": "sarathi"
      "sarathi_scheduler_config_batch_size_cap": 256
      "sarathi_scheduler_config_chunk_size": 1024
  cp512:
    name: "cp512"
    arguments:
      "replica_scheduler_config_type": "sarathi"
      "sarathi_scheduler_config_batch_size_cap": 256
      "sarathi_scheduler_config_chunk_size": 512
  cp256:
    name: "cp256"
    arguments:
      "replica_scheduler_config_type": "sarathi"
      "sarathi_scheduler_config_batch_size_cap": 256
      "sarathi_scheduler_config_chunk_size": 256
  cp128:
    name: "cp128"
    arguments:
      "replica_scheduler_config_type": "sarathi"
      "sarathi_scheduler_config_batch_size_cap": 256
      "sarathi_scheduler_config_chunk_size": 128
  triangle:
    name: "triangle"
    arguments:
      "global_scheduler_config_type": "triangle"
      "replica_scheduler_config_type": "triangle"
    

### 可用负载
workloads: # Dict[str, WorkloadConf]
  llama2_70b: 
    model_name: "meta-llama/Llama-2-70b-hf"
    tp_size: 4
    pp_size: 1
    num_prompts: 1280
    qps_list: [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
    slo: [6, 0.100]
    slo1: [4, 0.250]
    slo2: [15, 0.060]


  llama2_7b:
    model_name: "meta-llama/Llama-2-7b-hf"
    tp_size: 1
    pp_size: 1
    num_prompts: 1280
    qps_list: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    slo: [2, 0.050]
    slo1: [1.5, 0.100]
    slo2: [8, 0.040]
