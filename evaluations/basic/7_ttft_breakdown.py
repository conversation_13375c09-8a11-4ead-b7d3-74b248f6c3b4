# %%
import os
import pandas as pd
import dataclasses
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator


from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 9
matplotlib.rcParams['xtick.labelsize'] = 8
matplotlib.rcParams['ytick.labelsize'] = 9
matplotlib.rcParams['legend.fontsize'] = 8.5

@dataclasses.dataclass
class TTFTBreakdown:
    prefill_queue_time: float
    prefill_execution_time: float
    decoding_queue_time: float


class TTFTBreakdownFetcher:
    """获取 TTFT breakdown 结果的类"""

    def __init__(self, schedulers, qps):
        self.schedulers = schedulers
        self.qps = qps

    def _get_output_file(self, scheduler: str, qps: float) -> str:
        def get_latest_dir(base_dir):
            dirs = [
                d
                for d in os.listdir(base_dir)
                if os.path.isdir(os.path.join(base_dir, d))
            ]
            dirs.sort(reverse=True)
            return dirs[0] if dirs else None

        qps_str = f"{qps:.2f}"
        base_dir = os.path.join(".cache", scheduler, qps_str)
        dir = os.path.join(base_dir, get_latest_dir(base_dir))
        path = os.path.join(dir, "request_metrics.csv")
        return path

    def get_data_point(self, scheduler: str) -> TTFTBreakdown:
        data_file_path = self._get_output_file(scheduler, self.qps)

        if not os.path.exists(data_file_path):
            raise FileNotFoundError(f"File not found: {data_file_path}")

        columns = [
            "request_prefill_queue_time",
            "request_decode_queue_time",
            "prefill_e2e_time",
        ]
        data = pd.read_csv(data_file_path, usecols=columns)

        # 按照"prefill_e2e_time" 列进行排序

        data = data.sort_values(by="prefill_e2e_time")

        # 取出行索引在总量的 90%~91% 的行, 对他们每一列求平均值, 赋值到 TTFTBreakdown 返回
        start_index = int(len(data) * 0.9)
        end_index = int(len(data) * 0.91)
        data = data.iloc[start_index:end_index]
        data = data.mean()
        prefill_queue_time = float(data["request_prefill_queue_time"])
        decoding_queue_time = float(data["request_decode_queue_time"])
        prefill_execution_time = float(data["prefill_e2e_time"]) - prefill_queue_time - decoding_queue_time

        return TTFTBreakdown(
            prefill_queue_time, prefill_execution_time, decoding_queue_time
        )

    def fetch(self) -> list[tuple[str, TTFTBreakdown]]:
        data_points = []
        for scheduler in self.schedulers:
            data = self.get_data_point(scheduler)
            data_points.append((scheduler, data))
        return data_points


class TTFTBreakdownPlotter:
    def __init__(self, breakdowns: tuple[str, TTFTBreakdown]):
        self.breakdowns = breakdowns

    def plot(self):

        # Extract data for plotting
        categories = [
            "prefill_queue_time",
            "prefill_execution_time",
            "decoding_queue_time",
        ]
        labels = [
            "Prefill Queue Time",
            "Prefill Execution Time",
            "Decoding Queue Time",
        ]

        data = {
            category: [getattr(breakdown, category) for _, breakdown in self.breakdowns]
            for category in categories
        }

        # Define colors for each category
        colors = ["tab:blue", "tab:orange", "tab:green", "yellow"]

        # Create stacked bar plot
        x = np.arange(len(self.breakdowns))
        width = 0.5

        fig, ax = plt.subplots(figsize=(2.8, 2))
        bottom = np.zeros(len(self.breakdowns))

        for category, color, label in zip(categories, colors, labels):
            ax.bar(x, data[category], width, label=label, bottom=bottom,
                   color=color, edgecolor='black', linewidth=0.5)
            bottom += np.array(data[category])

        # Add labels and legend
        xticklabels = [scheduler.upper() for scheduler, breakdown in self.breakdowns]
        xticklabels[-1] = 'CP1K'
        ax.set_ylabel("P90 TTFT (s)")
        ax.set_xticks(x)
        ax.set_xticklabels(
            xticklabels
        )
        ax.set_ylim(0, 80)
        ax.yaxis.set_major_locator(MultipleLocator(20))

        # 设置y轴次要刻度 - 每个主刻度之间分成4份
        ax.yaxis.set_minor_locator(AutoMinorLocator(5))

        ax.legend(loc=(0.2, 0.62), frameon=False)

        plt.grid(axis='y', color='lightgray', linestyle='--')
        ax.set_axisbelow(True)

        # # Calculate and annotate multipliers
        # for i in range(len(self.breakdowns)):
        #     multiplier = bottom[i] / bottom[0]
        #     ax.text(
        #         x[i],
        #         bottom[i] + 5,  # Position above the bar
        #         f"{multiplier:.1f}x",
        #         ha="center",
        #         va="bottom",
        #         fontsize=8,
        #         color="black",
        #     )

        # Load configuration to get TTFT SLO value
        from simulation_utils import load_evaluation_conf

        conf = load_evaluation_conf("conf.yaml")
        workload = conf.workloads[conf.selected_workload]

        # Draw a red horizontal line at TTFT SLO
        ax.axhline(y=workload.get_ttft_slo(), color="red", linestyle="--", linewidth=1)

        current_dir = os.path.dirname(os.path.abspath(__file__))
        save_path = os.path.join(
            current_dir, ".cache", "figures", "small_ttft_breakdown_plot.pdf"
        )
        plt.savefig(
            save_path,
            dpi=300,
            bbox_inches="tight",
        )
        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

        # plt.show()


# %%
if __name__ == "__main__":
    schedulers = ["p4d4", "p5d3", "p6d2", "p7d1", "cp512", "cp1024"]
    qps = 12.00

    breakdowns = TTFTBreakdownFetcher(schedulers, qps).fetch()

    TTFTBreakdownPlotter(breakdowns).plot()
