#!/usr/bin/env python3
"""
Test script to verify that the updated configuration loading works correctly
with the new SLO group structure.
"""

import sys
import os

# Add the parent directories to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

def test_config_loading():
    """Test that configuration loading works with the new SLO structure"""
    try:
        from simulation_utils import load_evaluation_conf
        
        print("Testing configuration loading...")
        
        # Load the configuration
        conf = load_evaluation_conf('conf.yaml')
        print("✓ Configuration loaded successfully")
        
        # Test the selected workload
        selected_workload_name = conf.selected_workload
        print(f"✓ Selected workload: {selected_workload_name}")
        
        # Get the workload configuration
        workload = conf.workloads[selected_workload_name]
        print(f"✓ Workload configuration retrieved")
        
        # Test SLO access methods
        ttft_slo = workload.get_ttft_slo()
        tpot_slo = workload.get_tpot_slo()
        print(f"✓ TTFT SLO: {ttft_slo}")
        print(f"✓ TPOT SLO: {tpot_slo}")
        
        # Test that SLO groups are available
        print(f"✓ SLO groups available:")
        print(f"  - slo: {workload.slo}")
        print(f"  - slo1: {workload.slo1}")
        print(f"  - slo2: {workload.slo2}")
        
        # Verify that the values match the first SLO group
        if workload.slo:
            expected_ttft = workload.slo[0]
            expected_tpot = workload.slo[1]
            assert ttft_slo == expected_ttft, f"TTFT SLO mismatch: {ttft_slo} != {expected_ttft}"
            assert tpot_slo == expected_tpot, f"TPOT SLO mismatch: {tpot_slo} != {expected_tpot}"
            print("✓ SLO values correctly extracted from first SLO group")
        
        # Test all workloads
        print("\nTesting all workloads:")
        for name, workload in conf.workloads.items():
            print(f"  {name}:")
            print(f"    TTFT SLO: {workload.get_ttft_slo()}")
            print(f"    TPOT SLO: {workload.get_tpot_slo()}")
            print(f"    SLO groups: slo={workload.slo}, slo1={workload.slo1}, slo2={workload.slo2}")
        
        print("\n✅ All tests passed! Configuration loading works correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_loading()
    sys.exit(0 if success else 1)
