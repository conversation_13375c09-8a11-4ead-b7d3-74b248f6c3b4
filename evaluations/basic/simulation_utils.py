import os
import yaml
from copy import deepcopy
import multiprocessing
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
import matplotlib.pyplot as plt

import dataclasses
from pydantic import BaseModel, model_validator
from abc import ABC, abstractmethod

"""实验流程:
1. yaml 定义配置 + pydantic 载入配置
3. 根据配置生成脚本
4. 脚本执行
5. 提取 log 并绘图
"""


class WorkloadConf(BaseModel):
    model_name: str
    tp_size: int
    pp_size: int
    num_prompts: int
    qps_list: List[float]

    # New SLO group structure
    slo: Optional[List[float]] = None
    slo1: Optional[List[float]] = None
    slo2: Optional[List[float]] = None

    # Legacy fields for backward compatibility
    ttft_slo: Optional[float] = None
    tpot_slo: Optional[float] = None

    @model_validator(mode="after")
    def validate_slo_fields(self):
        """Ensure either legacy SLO fields or new SLO groups are provided, and populate legacy fields from new structure"""
        # If new SLO structure is provided, extract ttft_slo and tpot_slo from the first SLO group
        if self.slo is not None:
            if len(self.slo) != 2:
                raise ValueError(
                    "SLO group must contain exactly 2 values: [ttft_slo, tpot_slo]"
                )
            self.ttft_slo = self.slo[0]
            self.tpot_slo = self.slo[1]

        # Ensure ttft_slo and tpot_slo are set
        if self.ttft_slo is None or self.tpot_slo is None:
            raise ValueError(
                "Either legacy ttft_slo/tpot_slo fields or new slo group must be provided"
            )

        return self

    def get_ttft_slo(self) -> float:
        """Get TTFT SLO value, guaranteed to be available after validation"""
        assert self.ttft_slo is not None, "ttft_slo should be set after validation"
        return self.ttft_slo

    def get_tpot_slo(self) -> float:
        """Get TPOT SLO value, guaranteed to be available after validation"""
        assert self.tpot_slo is not None, "tpot_slo should be set after validation"
        return self.tpot_slo

    def get_all_slo_groups(self) -> Dict[str, Tuple[float, float]]:
        """Get all available SLO groups as a dictionary of {group_name: (ttft_slo, tpot_slo)}"""
        slo_groups = {}

        if self.slo is not None and len(self.slo) == 2:
            slo_groups["slo"] = (self.slo[0], self.slo[1])

        if self.slo1 is not None and len(self.slo1) == 2:
            slo_groups["slo1"] = (self.slo1[0], self.slo1[1])

        if self.slo2 is not None and len(self.slo2) == 2:
            slo_groups["slo2"] = (self.slo2[0], self.slo2[1])

        return slo_groups


class SchedulerConf(BaseModel):
    name: str
    arguments: Dict[str, Any]


class EvaluationConf(BaseModel):
    run: bool
    selected_workload: str
    selected_run_schedulers: List[str]

    schedulers: Dict[str, SchedulerConf]
    workloads: Dict[str, WorkloadConf]


def load_evaluation_conf(conf_path: str) -> EvaluationConf:
    """载入配置文件, 生成配置对象"""
    conf_path = os.path.abspath(conf_path)
    with open(conf_path, "r", encoding="utf-8") as f:
        conf = yaml.safe_load(f)
        print(f"Loaded config from {conf_path}")

    return EvaluationConf(**conf)

# -------------------- End of conf --------------------
# -------------------- EvaluationRunner --------------------

class CommandMaker:
    """脚本生成类
    - 初始化时提供脚本目录和脚本名,
    - 调用 add_command 添加一条命令, 填充命令的内容 (env, cmd, args, log, bg)
    - 退出时将所有命令写入脚本文件
    """

    def __init__(self, base_command):
        # 脚本的命令列表
        self.command_parts = [base_command]

    def add_args(
        self,
        args: str | list[str],
    ):
        if isinstance(args, str):
            self.command_parts.append(args)
        elif isinstance(args, list):
            self.command_parts.extend(args)
        else:
            raise TypeError(f"args must be str or list[str], not {type(args)}")

    def get_command(self):
        return " \\\n".join(self.command_parts)


class ScriptGenerator:
    def __init__(self, conf: EvaluationConf):
        self.conf = conf

        self.simulation_output_dir = os.path.join(os.getcwd(), ".cache")
        if not os.path.exists(self.simulation_output_dir):
            os.makedirs(self.simulation_output_dir)

    def _get_output_dir(self, scheduler: str, qps: str) -> str:
        # 路径结构: .cache/scheduler/qps/latest
        return os.path.join(self.simulation_output_dir, scheduler, qps)

    def generate_all_commands(self) -> List[str]:
        """根据配置生成所有命令"""
        commands: List[str] = []
        if not self.conf.run:
            return commands

        workload = self.conf.workloads[self.conf.selected_workload]

        # 基础命令
        base_command_maker = CommandMaker(f"cd ../..; ./env/bin/python -m vidur.main")
        base_command_maker.add_args(
            [
                f"--replica_config_device a100",
                f"--replica_config_network_device a100_dgx",
                f"--cluster_config_num_replicas 8",
                f"--request_generator_config_type synthetic",
                f"--synthetic_request_generator_config_num_requests {workload.num_prompts}",
                f"--interval_generator_config_type poisson",
                f"--length_generator_config_type trace",
                f"--trace_request_length_generator_config_max_tokens 4096",
                f"--trace_request_length_generator_config_trace_file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv",
                f"--metrics_config_store_token_completion_metrics",
                f"--replica_config_model_name {workload.model_name}",
                f"--replica_config_tensor_parallel_size {workload.tp_size}",
                f"--replica_config_num_pipeline_stages {workload.pp_size}",
            ]
        )

        for scheduler in self.conf.selected_run_schedulers:
            scheduler_command_maker = deepcopy(base_command_maker)
            # 添加调度器参数
            for key, value in self.conf.schedulers[scheduler].arguments.items():
                scheduler_command_maker.add_args(f"--{key} {value}")
            # 添加负载参数
            for qps in workload.qps_list:
                qps_command_maker = deepcopy(scheduler_command_maker)
                # 添加 QPS
                qps_str = f"{qps:.2f}"
                qps_command_maker.add_args(
                    f"--poisson_request_interval_generator_config_qps {qps_str}"
                )
                # 添加输出路径
                output_dir = self._get_output_dir(scheduler, qps_str)
                qps_command_maker.add_args(f"--metrics_config_output_dir {output_dir}")

                commands.append(qps_command_maker.get_command())

        return commands


class EvaluationRunner:
    def __init__(self, commands: List[str]):
        self.commands = commands

    def run(self):
        # 使用多进程运行(默认使用 90% 的核)
        pool = multiprocessing.Pool(processes=int(multiprocessing.cpu_count() * 0.9))
        pool.map(EvaluationRunner._run_command, self.commands)
        pool.close()

    @staticmethod
    def _run_command(command: str):
        os.system(command)

# -------------------- End of EvaluationRunner --------------------
# -------------------- Plotter --------------------

@dataclasses.dataclass
class BackendStyle:
    """后端配置类"""
    
    label: str
    color: str
    marker: str = "o"
    zorder: int = 0


class PlotterBase(ABC):
    """绘图工具基类"""

    def __init__(self, conf: EvaluationConf):
        self.conf = conf
        self.simulation_output_dir = os.path.join(os.getcwd(), ".cache")
        if not os.path.exists(self.simulation_output_dir):
            raise FileNotFoundError(
                "No cache directory found. Please run evaluation first."
            )

        self.figure_dir = os.path.join(os.getcwd(), ".cache",  "figures")
        if not os.path.exists(self.figure_dir):
            os.makedirs(self.figure_dir)

        self.workload = self.conf.workloads[self.conf.selected_workload]
        self.percentile = 0.9

    @abstractmethod
    def plot(self):
        """画图接口, 由子类实现"""
        pass

    def _check_success(self): ...

    def _check_complete(self): ...

    # 获取延迟和 QPS 的结果
    def _fetch_all_tail_latency_qps_results(
        self, selected_plot_schedulers: List[str]
    ) -> List[Tuple[str, List[float], List[float]]]:
        """获取所有调度器的延迟和 QPS 的结果
        结果格式: [(scheduler, ttft_list, tpot_list), ...]
        其中, ttft_list 和 tpot_list 对应 qps
        """
        results: List[Tuple[str, List[float], List[float]]] = []
        for scheduler in selected_plot_schedulers:
            ttfts: List[float] = []
            tpots: List[float] = []
            for qps in self.workload.qps_list:
                qps_str = f"{qps:.2f}"
                ttft = self._fetch_tail_latency(
                    scheduler,
                    qps_str,
                    "plots/prefill_e2e_time.csv",
                    "prefill_e2e_time",
                )
                tpot = self._fetch_tail_latency(
                    scheduler,
                    qps_str,
                    "plots/decode_time_execution_plus_preemption_normalized.csv",
                    "decode_time_execution_plus_preemption_normalized",
                )
                ttfts.append(ttft)
                tpots.append(tpot)
            results.append((scheduler, ttfts, tpots))
        return results

    def _fetch_tail_latency(
        self, scheduler: str, qps: str, file_name: str, metric_name: str
    ) -> int | float:
        """获取延迟或 QPS 的值"""
        path = os.path.join(self._get_output_dir(scheduler, qps), file_name)
        assert os.path.exists(path), f"File {path} not found"
        df = pd.read_csv(path)
        return self._get_percentile(df, metric_name, self.percentile)

    def _fetch_all_latency_qps_results(
        self,  selected_plot_schedulers: List[str]
    ) -> List[Tuple[str, List[List[float]], List[List[float]], List[float]]]:
        """获取所有调度器的延迟和 QPS 的结果
        结果格式: [(scheduler, list of ttft_list, list of tpot_list, attainment), ...]
        其中, list of ttft_list/tpot_list 对应 qps, ttft_list 和 tpot_list 相同下标对应相同请求
        """
        results: List[Tuple[str, List[List[float]], List[List[float]], List[float]]] = (
            []
        )
        for scheduler in selected_plot_schedulers:
            all_ttfts: List[List[float]] = []
            all_tpots: List[List[float]] = []
            all_attained_rate: List[float] = []
            for qps in self.workload.qps_list:
                qps_str = f"{qps:.2f}"
                ttfts = self._fetch_latency(
                    scheduler,
                    qps_str,
                    "request_metrics.csv",
                    "prefill_e2e_time",
                )
                tpots = self._fetch_latency(
                    scheduler,
                    qps_str,
                    "request_metrics.csv",
                    "decode_time_execution_plus_preemption_normalized",
                )
                # 计算满足率
                num_attained = sum(
                    [
                        ttft <= self.workload.get_ttft_slo()
                        and tpot <= self.workload.get_tpot_slo()
                        for ttft, tpot in zip(ttfts, tpots)
                    ]
                )
                attained_rate = num_attained / len(ttfts) * 100
                all_ttfts.append(ttfts)
                all_tpots.append(tpots)
                all_attained_rate.append(attained_rate)

            results.append((scheduler, all_ttfts, all_tpots, all_attained_rate))
        return results

    def _fetch_all_latency_qps_results_with_all_slo_groups(
        self, selected_plot_schedulers: List[str]
    ) -> List[Tuple[str, List[List[float]], List[List[float]], List[float], List[Dict[str, float]]]]:
        """获取所有调度器的延迟和 QPS 的结果，包含所有 SLO 组的满足率
        结果格式: [(scheduler, list of ttft_list, list of tpot_list, attainment, all_slo_satisfaction_rates), ...]
        其中, list of ttft_list/tpot_list 对应 qps, ttft_list 和 tpot_list 相同下标对应相同请求
        all_slo_satisfaction_rates 是每个 QPS 对应的所有 SLO 组满足率字典列表
        """
        results: List[Tuple[str, List[List[float]], List[List[float]], List[float], List[Dict[str, float]]]] = []

        for scheduler in selected_plot_schedulers:
            all_ttfts: List[List[float]] = []
            all_tpots: List[List[float]] = []
            all_attained_rate: List[float] = []
            all_slo_satisfaction_rates: List[Dict[str, float]] = []

            for qps in self.workload.qps_list:
                qps_str = f"{qps:.2f}"
                ttfts = self._fetch_latency(
                    scheduler,
                    qps_str,
                    "request_metrics.csv",
                    "prefill_e2e_time",
                )
                tpots = self._fetch_latency(
                    scheduler,
                    qps_str,
                    "request_metrics.csv",
                    "decode_time_execution_plus_preemption_normalized",
                )

                # 计算第一个 SLO 组的满足率（用于向后兼容）
                num_attained = sum(
                    [
                        ttft <= self.workload.get_ttft_slo()
                        and tpot <= self.workload.get_tpot_slo()
                        for ttft, tpot in zip(ttfts, tpots)
                    ]
                )
                attained_rate = num_attained / len(ttfts) * 100

                # 计算所有 SLO 组的满足率
                slo_satisfaction_rates = self._calculate_satisfaction_rates_for_all_slo_groups(ttfts, tpots)

                all_ttfts.append(ttfts)
                all_tpots.append(tpots)
                all_attained_rate.append(attained_rate)
                all_slo_satisfaction_rates.append(slo_satisfaction_rates)

            results.append((scheduler, all_ttfts, all_tpots, all_attained_rate, all_slo_satisfaction_rates))

        return results

    def _fetch_latency(
        self, scheduler: str, qps: str, file_name: str, metric_name: str
    ) -> List[float]:
        """获取延迟或 QPS 的值"""
        path = os.path.join(self._get_output_dir(scheduler, qps), file_name)
        assert os.path.exists(path), f"File {path} not found"
        df = pd.read_csv(path)
        result = df[metric_name].tolist()
        return result

    def _calculate_satisfaction_rates_for_all_slo_groups(
        self, ttfts: List[float], tpots: List[float]
    ) -> Dict[str, float]:
        """Calculate satisfaction rates for all SLO groups"""
        slo_groups = self.workload.get_all_slo_groups()
        satisfaction_rates = {}

        for group_name, (ttft_slo, tpot_slo) in slo_groups.items():
            num_satisfied = sum(
                [
                    ttft <= ttft_slo and tpot <= tpot_slo
                    for ttft, tpot in zip(ttfts, tpots)
                ]
            )
            satisfaction_rate = (
                num_satisfied / len(ttfts) * 100 if len(ttfts) > 0 else 0.0
            )
            satisfaction_rates[group_name] = satisfaction_rate

        return satisfaction_rates

    def display_satisfaction_rates_summary(
        self,
        results: List[Tuple[str, List[List[float]], List[List[float]], List[float], List[Dict[str, float]]]],
        workload_name: str
    ):
        """Display formatted satisfaction rate results for all SLO groups in terminal"""
        print("\n" + "="*80)
        print(f"SLO SATISFACTION RATE ANALYSIS - Workload: {workload_name}")
        print("="*80)

        # Get SLO group information for header
        slo_groups = self.workload.get_all_slo_groups()
        slo_group_names = list(slo_groups.keys())

        # Print SLO group definitions
        print("\nSLO Group Definitions:")
        for group_name, (ttft_slo, tpot_slo) in slo_groups.items():
            print(f"  {group_name}: TTFT ≤ {ttft_slo:.3f}s, TPOT ≤ {tpot_slo:.3f}s")

        print("\n" + "-"*80)

        # Create header
        header = f"{'Scheduler':<12} {'QPS':<6}"
        for group_name in slo_group_names:
            header += f" {group_name + ' Rate':<12}"
        print(header)
        print("-"*80)

        # Display results for each scheduler
        for scheduler, _, _, _, all_slo_satisfaction_rates in results:
            for qps_idx, qps in enumerate(self.workload.qps_list):
                qps_satisfaction_rates = all_slo_satisfaction_rates[qps_idx]

                row = f"{scheduler:<12} {qps:<6.2f}"
                for group_name in slo_group_names:
                    rate = qps_satisfaction_rates.get(group_name, 0.0)
                    row += f" {rate:<11.1f}%"
                print(row)

            # Add separator between schedulers
            if scheduler != results[-1][0]:  # Not the last scheduler
                print("-"*40)

        print("="*80)

    def analyze_and_display_slo_satisfaction_rates(self, selected_plot_schedulers: List[str]):
        """Analyze and display SLO satisfaction rates for all SLO groups"""
        # Calculate satisfaction rates for all SLO groups
        results = self._fetch_all_latency_qps_results_with_all_slo_groups(selected_plot_schedulers)

        # Display the results in terminal
        workload_name = self.conf.selected_workload
        self.display_satisfaction_rates_summary(results, workload_name)

        return results

    def _fetch_tpot_interference_data(
        self, scheduler: str, qps: str
    ) -> Tuple[List[float], List[float]]:
        """获取 tpot 和干扰强度"""
        path = os.path.join(self._get_output_dir(scheduler, qps), "request_metrics.csv")
        assert os.path.exists(path), f"File {path} not found"
        df = pd.read_csv(path)
        # 获取 tpot 的推理时间
        interference = df["request_num_interference_tokens_per_token"].tolist()
        tpot = df["decode_time_execution_plus_preemption_normalized"].tolist()
        return interference, tpot

    # 辅助函数
    def _get_percentile(self, df, column_name, percentile) -> int | float:
        """获取某一列的百分位数"""
        return df[column_name].quantile(percentile)

    def _get_output_dir(self, scheduler: str, qps: str) -> str:
        # 路径结构: .cache/scheduler/qps/latest
        def _get_latest(dir: str) -> str:
            dirs = os.listdir(dir)
            dirs.sort()
            return dirs[-1]

        dir = os.path.join(self.simulation_output_dir, scheduler, qps)
        dir = os.path.join(dir, _get_latest(dir))
        return dir


# -------------------- End of Plotter --------------------
