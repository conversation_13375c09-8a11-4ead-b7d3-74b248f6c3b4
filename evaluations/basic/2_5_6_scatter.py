# %%
import random
import numpy as np
import os
from typing import List, Dict, Any, Tuple, Optional
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator
from simulation_utils import (
    EvaluationConf,
    load_evaluation_conf,
    PlotterBase,
    BackendStyle,
)
from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10

# Fig 2
# matplotlib.rcParams['legend.fontsize'] = 9

# Fig 5,6
matplotlib.rcParams['legend.fontsize'] = 10


# -------------------- ScatterPlotter --------------------
class ScatterPlotter(PlotterBase):
    """散点图绘制工具"""

    def __init__(
        self,
        conf: EvaluationConf,
        selected_plot_schedulers: List[str],
        backend_styles: Dict[str, BackendStyle],
        plot_name: str,
        limit: List[List[float]],
        legend_pos: Tuple[float, float],
        legend_ncol: int,
        major_locator: int,
        marker_size: int,
        sample_rate: float,
        qps_list: List[float],
    ):
        super().__init__(conf)
        self._set_style()
        self.selected_plot_schedulers = selected_plot_schedulers
        self.backend_styles = backend_styles
        self.plot_name = plot_name
        self.limit = limit
        self.legend_pos = legend_pos
        self.legend_ncol = legend_ncol
        self.major_locator = major_locator
        self.marker_size = marker_size
        self.sample_rate = sample_rate
        self.qps_list = qps_list

    def _set_style(self):
        plt.rcParams["font.size"] = 12

    def plot(self):
        # 绘制散点图
        self._plot_scatter()

    def _plot_scatter(self):
        results = self._fetch_all_latency_qps_results(self.selected_plot_schedulers)
        # 针对每个 QPS 绘制散点图
        for i, qps in enumerate(self.workload.qps_list):
            if qps not in self.qps_list:
                continue
            qps_str = f"{qps:.2f}"

            # 过滤出当前 QPS 的结果
            filtered_results = [
                (scheduler, all_ttfts[i], all_tpots[i], all_attained_rate[i])
                for scheduler, all_ttfts, all_tpots, all_attained_rate in results
            ]
            # 绘制散点图
            self._make_scatter_figure(qps_str, filtered_results)

    def _get_random_subset_ttft_tpot(self, tpots, ttfts, ratio=0.9):
        assert len(tpots) == len(ttfts)
        sample_size = int(len(tpots) * ratio)
        indices = np.random.choice(len(tpots), sample_size, replace=False)
        sampled_tpots = [tpots[i] for i in indices]
        sampled_ttfts = [ttfts[i] for i in indices]
        return sampled_tpots, sampled_ttfts

    def _make_scatter_figure(
        self, qps: str, results: List[Tuple[str, List[float], List[float], float]]
    ) -> None:
        fig, ax = plt.subplots(figsize=(3, 2), dpi=300)

        # 延迟散点图
        for scheduler, ttfts, tpots, attained_rate in results:
            scheduler_name = self.conf.schedulers[scheduler].name
            style = self.backend_styles[scheduler_name]
            sampled_tpots, sampled_ttfts = self._get_random_subset_ttft_tpot(
                tpots, ttfts, self.sample_rate)
            print(f"{style.label}: {len(sampled_ttfts)} from {len(ttfts)}")
            ax.scatter(
                sampled_tpots,
                sampled_ttfts,
                label=f"{style.label} ({attained_rate:.0f}%)",
                marker=style.marker,
                s=self.marker_size,
                color=style.color,
                edgecolors='black',
                linewidths=0.5,
                zorder=style.zorder,
            )

        # SLO 线
        ttft_slo, tpot_slo = self.workload.get_ttft_slo(), self.workload.get_tpot_slo()
        plt.plot(
            [0, tpot_slo], [ttft_slo, ttft_slo], color="r", linestyle="--", zorder=10
        )
        plt.plot(
            [tpot_slo, tpot_slo], [0, ttft_slo], color="r", linestyle="--", zorder=10
        )

        # 设置坐标轴范围
        ax.set_xlim(self.limit[0][0], self.limit[0][1])
        ax.set_ylim(self.limit[1][0], self.limit[1][1])

        # 坐标轴
        ax.set_xlabel("TPOT (s)")
        ax.set_ylabel("TTFT (s)")

        ax.yaxis.set_major_locator(MultipleLocator(self.major_locator))

        # 图例
        ax.legend(loc=self.legend_pos, ncol=self.legend_ncol, frameon=False,
                  markerfirst=False, handlelength=1)
        # ax.legend(frameon=False, markerfirst=False)
        # ax.legend(
        #     frameon=False,
        #     bbox_to_anchor=(0.95, 1.35, 0, 0),
        #     facecolor="none",
        #     edgecolor="none",
        #     ncol=2,
        # )
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))
        ax.yaxis.set_minor_locator(AutoMinorLocator(5))

        plt.tight_layout()
        # plt.show()

        # 保存
        save_path = os.path.join(self.figure_dir, f"small_{self.plot_name}_scatter_{qps}.pdf")
        fig.savefig(save_path)
        print(f"Figure saved to {save_path}")
        plt.close(fig)

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

# %%
if __name__ == "__main__":

    conf = load_evaluation_conf("conf.yaml")

    # 画图配置
    multiple_selected_plot_schedulers = [
        ["p6d2", "cp1024"],
        ["p6d2", "cp1024", "triangle"],
        ["cp128", "cp256", "cp512", "cp1024", "vllm"],
        ["p4d4", "p5d3", "p6d2", "p7d1", "cp1024"],
    ]
    multiple_backend_styles = [
        {
            "p6d2": BackendStyle(
                label="Disagg.", marker="^", color="tab:orange", zorder=2
            ),
            "cp1024": BackendStyle(
                label="Agg.", marker="s", color="tab:green", zorder=1
            ),
        },
        {
            "p6d2": BackendStyle(
                label="Disagg.", marker="^", color="tab:orange", zorder=2
            ),
            "cp1024": BackendStyle(
                label="Agg.", marker="s", color="tab:green", zorder=1
            ),
            "triangle": BackendStyle(label="Ideal", marker="o", color="tab:blue", zorder=3),
        },
        {
            "cp128": BackendStyle(label="CP128", marker="1", color="tab:purple", zorder=5),
            "cp256": BackendStyle(label="CP256", marker="^", color="tab:orange", zorder=4),
            "cp512": BackendStyle(label="CP512", marker="s", color="tab:green", zorder=3),
            "cp1024": BackendStyle(label="CP1024", marker="o", color="tab:brown", zorder=2),
            "vllm": BackendStyle(label="VLLM", marker="P", color="tab:blue", zorder=1),
        },
        {
            "p4d4": BackendStyle(label="P4D4", marker="1", color="tab:purple", zorder=4),
            "p5d3": BackendStyle(label="P5D3", marker="^", color="tab:orange", zorder=5),
            "p6d2": BackendStyle(label="P6D2", marker="s", color="tab:green", zorder=3),
            "p7d1": BackendStyle(label="P7D1", marker="o", color="tab:brown", zorder=2),
            "cp1024": BackendStyle(label="CP1024", marker="D", color="tab:blue", zorder=0),
        },
    ]

    multiple_plot_names = [
        "p6d2_cp1024",
        "p6d2_cp1024_triangle",
        "cp128_cp256_cp512_cp1024_vllm",
        "p4d4_p5d3_p6d2_p7d1_cp1024",
    ]
    multiple_limits = [
        [[0, 0.250], [0, 20]],
        [[0, 0.250], [0, 20]],
        [[0, 0.500], [0, 50]],
        [[0, 0.250], [0, 80]],
    ]
    multiple_legend_pos = [
        None,
        None,
        None,
        None,
    ]
    multiple_legend_ncol = [
        1,
        1,
        1,
        1,
    ]
    multiple_major_locators = [
        # 2,
        5,
        5,
        10,
        20,
    ]
    multiple_marker_sizes = [
        50,
        30,
        30,
        30,
    ]
    sample_rates = [
        0.05,
        0.05,
        0.1,
        0.1
    ]
    multiple_qps_list = [
        [6.00],
        [12.00],
        [12.00],
        [12.00],
    ]

    for selected_plot_schedulers, backend_styles, plot_name, limit, \
        legend_pos, legend_ncol, major_locator, marker_size, sample_rate, \
        qps_list in zip(
        multiple_selected_plot_schedulers,
        multiple_backend_styles,
        multiple_plot_names,
        multiple_limits,
        multiple_legend_pos,
        multiple_legend_ncol,
        multiple_major_locators,
        multiple_marker_sizes,
        sample_rates,
        multiple_qps_list,
    ):
        plotter = ScatterPlotter(
            conf=conf,
            selected_plot_schedulers=selected_plot_schedulers,
            backend_styles=backend_styles,
            plot_name=plot_name,
            limit=limit,
            legend_pos=legend_pos,
            legend_ncol=legend_ncol,
            major_locator=major_locator,
            marker_size=marker_size,
            sample_rate=sample_rate,
            qps_list=qps_list,
        )
        plotter.plot()
