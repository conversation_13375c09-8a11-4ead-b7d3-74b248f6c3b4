# %%
import sys
import os
import dataclasses

import numpy as np

from typing import Tu<PERSON>, List, Dict, Any


import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator

if "../.." not in sys.path:
    sys.path.append("../..")

if "." not in sys.path:
    sys.path.append(".")

from vidur.entities import Request

from vidur.entities import Batch, BatchStage, ExecutionTime
from vidur.execution_time_predictor import BaseExecutionTimePredictor
from vidur.execution_time_predictor import ExecutionTimePredictorRegistry
from vidur.config import SimulationConfig

from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 8
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 9


@dataclasses.dataclass
class IterationBreakdown:
    linear_time: float
    prefill_attn_time: float
    decoding_attn_time: float
    other_time: float


class IterationInsightFetcher:

    def __init__(
        self,
    ) -> None:
        # 构造预测器 (来自 base_global_scheduler.py)
        config: SimulationConfig = SimulationConfig.create_from_cli_args()
        self.execution_time_predictor: BaseExecutionTimePredictor = (
            ExecutionTimePredictorRegistry.get(
                config.execution_time_predictor_config.get_type(),
                predictor_config=config.execution_time_predictor_config,
                replica_config=config.cluster_config.replica_config,
                replica_scheduler_config=config.cluster_config.replica_scheduler_config,
                metrics_config=config.metrics_config,
            )
        )

    def build_batches(self) -> List[Batch]:
        # 这 4 个 Batch 分别是纯 decoding, chunk size 为 256, chunk size 为 512, chunk size 为 1024
        batch_size = 16

        decoding_request = Request(
            arrived_at=0,
            num_prefill_tokens=3000,
            num_decode_tokens=256,
            num_processed_tokens=3000,
        )
        decoding_request._is_prefill_complete = True
        half_prefill_request = Request(
            arrived_at=0,
            num_prefill_tokens=3000,
            num_decode_tokens=256,
            num_processed_tokens=1500,
        )

        # 0. 纯 decoding
        batch0 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size)],
            num_tokens=[1 for i in range(batch_size)],
        )
        # 1. chunk size 为 128
        batch1 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size - 1)]
            + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size - 1)] + [128 - (batch_size - 1)],
        )
        # 2. chunk size 为 256
        batch2 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size - 1)]
            + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size - 1)] + [256 - (batch_size - 1)],
        )
        # 3. chunk size 为 512
        batch3 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size - 1)]
            + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size - 1)] + [512 - (batch_size - 1)],
        )
        # 4. chunk size 为 1024
        batch4 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size - 1)]
            + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size - 1)] + [1024 - (batch_size - 1)],
        )

        return [batch0, batch1, batch2, batch3, batch4]

    def fetch_execution_time(self, batches: List[Batch]) -> List[ExecutionTime]:
        # 使用时间预测器预测 Batch
        return [
            self.execution_time_predictor.get_execution_time(batch, 0)
            for batch in batches
        ]

    def fetch_breakdowns(
        self, execution_times: List[ExecutionTime]
    ) -> List[IterationBreakdown]:
        # 解析预测结果
        # 1. 其他时间
        # 2. attention 时间
        # 3. linear 时间
        # 4. 总时间
        # 5. 模型时间
        # 6. chunk size
        # 7. batch size

        breakdowns = []

        for i, execution_time in enumerate(execution_times):
            breakdown = IterationBreakdown(
                linear_time=1000 * execution_time.total_linear_time,
                prefill_attn_time=1000 * execution_time.total_prefill_attn_time,
                decoding_attn_time=1000 * execution_time.total_decoding_attn_time,
                other_time=1000 * float(execution_time.total_time)
                - (
                    1000 * execution_time.total_linear_time
                    + 1000 * execution_time.total_prefill_attn_time
                    + 1000 * execution_time.total_decoding_attn_time
                ),
            )
            breakdowns.append(breakdown)

        return breakdowns

    def fetch_result(self):
        # 使用时间预测器预测 Batch
        batches = self.build_batches()
        execution_times = self.fetch_execution_time(batches)
        breakdowns = self.fetch_breakdowns(execution_times)
        return breakdowns


class BreakdownPlotter:

    def __init__(self, breakdowns):
        self.breakdowns = breakdowns
        self._set_styles()

    def _set_styles(self):
        plt.rcParams.update({"font.size": 8})

    def plot(self):

        # Extract data for plotting
        categories = [
            "linear_time",
            "prefill_attn_time",
            "decoding_attn_time",
            "other_time",
        ]
        data = {
            category: [getattr(breakdown, category) for breakdown in self.breakdowns]
            for category in categories
        }

        # Define colors for each category
        colors = ["tab:blue", "tab:orange", "tab:green", "tab:gray"]
        labels = [
            "Linear Time",
            # "Prefill Attention Time",
            "Prefill Attn Time",
            # "Decoding Attention Time",
            "Decoding Attn Time",
            "Other Time",
        ]

        # Create stacked bar plot
        x = np.arange(len(self.breakdowns))
        width = 0.5

        fig, ax = plt.subplots(figsize=(2.8, 2))
        bottom = np.zeros(len(self.breakdowns))

        for category, color, label in zip(categories, colors, labels):
            ax.bar(x, data[category], width, label=label, bottom=bottom,
                   color=color, edgecolor='black', linewidth=0.5)
            bottom += np.array(data[category])

        # Add labels and legend
        ax.set_ylabel("Time (ms)")
        ax.set_xticks(x)
        ax.set_xticklabels(["Decoding", "CP128", "CP256", "CP512", "CP1K"])
        # ax.set_ylim(0, max(bottom) * 1.2)
        ax.set_ylim(0, 300)
        ax.yaxis.set_major_locator(MultipleLocator(100))
        ax.yaxis.set_minor_locator(AutoMinorLocator(5))

        ax.legend(frameon=False)

        plt.grid(axis='y', color='lightgray', linestyle='--')
        ax.set_axisbelow(True)

        # Calculate and annotate multipliers
        for i in range(len(self.breakdowns)):
            multiplier = bottom[i] / bottom[0]
            ax.text(
                x[i],
                bottom[i] + 5,  # Position above the bar
                f"{multiplier:.1f}x",
                ha="center",
                va="bottom",
                fontsize=9,
                color="black",
            )

        # plt.grid(axis='x', color='lightgray', linestyle='--')
        plt.grid(axis='y', color='lightgray', linestyle='--')

        current_dir = os.path.dirname(os.path.abspath(__file__))
        save_path = os.path.join(
            current_dir, ".cache", "figures", "small_iteration_breakdown_plot.pdf"
        )
        plt.savefig(
            save_path,
            dpi=300,
            bbox_inches="tight",
        )
        print(f"Saving figure to {save_path}")
        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

        # plt.show()


# %%
if __name__ == "__main__":
    if os.getcwd().endswith("basic"):
        current_dir = os.getcwd()
        os.chdir("../..")
    else:
        current_dir = os.getcwd()
        assert current_dir.endswith(
            "vidur"
        ), "Current directory should be project root or 'basic'"

    sys.argv = [
        "notebook",
        "--replica_config_device",
        "a100",
        "--replica_config_network_device",
        "a100_dgx",
        "--replica_config_model_name",
        "meta-llama/Llama-2-70b-hf",
        "--cluster_config_num_replicas",
        "4",
        "--replica_config_tensor_parallel_size",
        "4",
        "--replica_config_num_pipeline_stages",
        "1",
        "--request_generator_config_type",
        "synthetic",
        "--length_generator_config_type",
        "trace",
        "--interval_generator_config_type",
        "poisson",
        "--trace_request_length_generator_config_max_tokens",
        "4096",
        "--trace_request_length_generator_config_trace_file",
        "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv",
        "--synthetic_request_generator_config_num_requests",
        "1280",
        "--poisson_request_interval_generator_config_qps",
        "10",
        "--replica_scheduler_config_type",
        "sarathi",
        "--sarathi_scheduler_config_batch_size_cap",
        "256",
        "--sarathi_scheduler_config_chunk_size",
        "1024",
    ]

    breakdowns = IterationInsightFetcher().fetch_result()
    BreakdownPlotter(breakdowns).plot()
