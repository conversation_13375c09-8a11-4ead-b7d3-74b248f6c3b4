# %%
# 引入模块
import sys
import json
import os
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator

from typing import Any

from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

# %%
# 定义数据获取类
class CDFFetcher:
    def __init__(
        self, scheduler: str, qps: float, file_name: str, columns: list[str]
    ) -> None:
        dir = os.path.join(os.path.dirname(__file__), ".cache", scheduler, f"{qps:.2f}")
        self.file_path = os.path.join(dir, self._get_latest_dir(dir), file_name)
        self.columns = columns

    def fetch(self) -> tuple[list[float], list[float]]:
        data = pd.read_csv(self.file_path)
        data = data[self.columns]
        data = data.dropna()
        x = data[self.columns[0]].values.tolist()
        y = data[self.columns[1]].values.tolist()
        return x, y

    def _get_latest_dir(self, dir: str) -> str:
        dirs = os.listdir(dir)
        dirs.sort()
        return dirs[-1]


# %%
# 定义画图类
class CDFPlotter:
    def __init__(self, data: tuple[list[float],list[float]],
                 slo: float, xlim: float, x_major: float,
                 anno_text: tuple[float, float],
                 anno_point: tuple[float, float],
                 x_title: str,
                 file_name: str
        ) -> None:
        self.x = data[0]
        self.y = data[1]
        self.slo = slo
        self.xlim = xlim
        self.x_major = x_major
        self.anno_text = anno_text
        self.anno_point = anno_point
        self.x_title = x_title
        self.file_name = file_name

    def plot(self) -> None:
        fig, ax = plt.subplots(figsize=(3, 2))
        plt.rcParams.update({'font.size': 10})
        plt.plot(self.x, self.y, color="tab:blue")
        plt.xlabel(self.x_title)
        plt.ylabel("CDF")
        
        plt.xlim(0, self.xlim)
        plt.ylim(0, 1)
        
        plt.axvline(self.slo, color='red', linestyle='--', label=f"SLO: {self.slo}s")
        plt.tight_layout()

        ax.yaxis.set_major_locator(MultipleLocator(0.2))
        print(f'{self.x_major=}')
        ax.xaxis.set_major_locator(MultipleLocator(self.x_major))

        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))
        
        plt.grid(axis='y', color='lightgray', linestyle='--')
        plt.grid(axis='x', color='lightgray', linestyle='--')
        ax.set_axisbelow(True)

        plt.annotate("SLO",
                     xy=self.anno_point,  # 注释的坐标位置
                     xytext=self.anno_text,  # 文本的显示位置
                     arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

        save_path = os.path.join(".cache", "figures", f"{self.file_name}.pdf")
        plt.savefig(save_path)
        plt.show()

        plt.close()
        plt.clf()

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

# %%
# 执行
if __name__ == "__main__":
    # 获取数据
    inputs = [
        (("cp1024", 12, "plots/prefill_e2e_time.csv", ["prefill_e2e_time", "cdf"]), 6, 8, 2, (4, 0.2), (6, 0), "TTFT (s)", "cp_ttft_cdf"),
        (("p6d2", 12, "plots/decode_time_execution_plus_preemption_normalized.csv", ["decode_time_execution_plus_preemption_normalized", "cdf"]), 0.100, 0.12, 0.02, (0.07, 0.2), (0.1, 0), "TPOT (s)", "pd_tpot_cdf"),
    ]
    
    plot_inputs = [
        (CDFFetcher(*fetch_input), slo, xlim, x_major, anno_text, anno_point, x_title, file_name)
        for (fetch_input, slo, xlim, x_major, anno_text, anno_point, x_title, file_name) in inputs
    ]
    
    # 画图
    for (fetcher, slo, xlim, x_major, anno_text, anno_point, x_title,
         file_name) in plot_inputs:
        data = fetcher.fetch()
        plotter = CDFPlotter(data, slo, xlim, x_major, anno_text, anno_point,
                             x_title, file_name)
        plotter.plot()
    
    

# %%
