#!/usr/bin/env python3
"""
Test script to verify the new multi-SLO satisfaction rate calculation and display functionality.
"""

import sys
import os

# Add the parent directories to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

def test_slo_groups_functionality():
    """Test the SLO groups functionality"""
    try:
        from simulation_utils import load_evaluation_conf, PlotterBase
        
        print("Testing multi-SLO satisfaction rate functionality...")
        
        # Load the configuration
        conf = load_evaluation_conf('conf.yaml')
        print("✓ Configuration loaded successfully")
        
        # Test the workload SLO groups
        workload = conf.workloads[conf.selected_workload]
        slo_groups = workload.get_all_slo_groups()
        
        print(f"✓ Found {len(slo_groups)} SLO groups:")
        for group_name, (ttft_slo, tpot_slo) in slo_groups.items():
            print(f"  - {group_name}: TTFT ≤ {ttft_slo}s, TPOT ≤ {tpot_slo}s")
        
        # Test satisfaction rate calculation with mock data
        print("\n✓ Testing satisfaction rate calculation with mock data:")
        
        # Create a mock plotter to test the functionality
        class MockPlotter(PlotterBase):
            def plot(self):
                pass
        
        mock_plotter = MockPlotter(conf)
        
        # Test with mock latency data
        mock_ttfts = [5.0, 7.0, 3.0, 8.0, 4.0]  # Some above and below different SLO thresholds
        mock_tpots = [0.08, 0.12, 0.05, 0.15, 0.09]  # Some above and below different SLO thresholds
        
        satisfaction_rates = mock_plotter._calculate_satisfaction_rates_for_all_slo_groups(
            mock_ttfts, mock_tpots
        )
        
        print("  Mock satisfaction rates:")
        for group_name, rate in satisfaction_rates.items():
            print(f"    {group_name}: {rate:.1f}%")
        
        # Test the display functionality with mock results
        print("\n✓ Testing display functionality:")
        
        # Create mock results structure
        mock_results = []
        for scheduler in ["test_scheduler1", "test_scheduler2"]:
            all_ttfts = [[5.0, 7.0, 3.0], [6.0, 8.0, 4.0]]  # 2 QPS points
            all_tpots = [[0.08, 0.12, 0.05], [0.09, 0.13, 0.06]]  # 2 QPS points
            all_attained_rate = [66.7, 33.3]  # Mock rates for 2 QPS points
            all_slo_satisfaction_rates = []
            
            for ttfts, tpots in zip(all_ttfts, all_tpots):
                slo_rates = mock_plotter._calculate_satisfaction_rates_for_all_slo_groups(ttfts, tpots)
                all_slo_satisfaction_rates.append(slo_rates)
            
            mock_results.append((scheduler, all_ttfts, all_tpots, all_attained_rate, all_slo_satisfaction_rates))
        
        # Test display
        mock_plotter.display_satisfaction_rates_summary(mock_results, "test_workload")
        
        print("\n✅ All tests passed! Multi-SLO satisfaction rate functionality works correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test that existing functionality still works"""
    try:
        from simulation_utils import load_evaluation_conf, PlotterBase
        
        print("\nTesting backward compatibility...")
        
        # Load the configuration
        conf = load_evaluation_conf('conf.yaml')
        workload = conf.workloads[conf.selected_workload]
        
        # Test that legacy methods still work
        ttft_slo = workload.get_ttft_slo()
        tpot_slo = workload.get_tpot_slo()
        
        print(f"✓ Legacy SLO access works: TTFT={ttft_slo}, TPOT={tpot_slo}")
        
        # Test that the original method signature is preserved
        class TestPlotter(PlotterBase):
            def plot(self):
                pass
        
        test_plotter = TestPlotter(conf)
        
        # The original method should still exist and work
        # (We can't test it fully without actual data files, but we can check the method exists)
        assert hasattr(test_plotter, '_fetch_all_latency_qps_results'), "Original method should still exist"
        assert hasattr(test_plotter, '_fetch_all_latency_qps_results_with_all_slo_groups'), "New method should exist"
        
        print("✓ Both original and new methods exist")
        print("✓ Backward compatibility maintained")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("="*60)
    print("MULTI-SLO SATISFACTION RATE ANALYSIS TEST")
    print("="*60)
    
    success1 = test_slo_groups_functionality()
    success2 = test_backward_compatibility()
    
    if success1 and success2:
        print("\n🎉 All tests passed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
