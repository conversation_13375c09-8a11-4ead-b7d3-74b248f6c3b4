# %%
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator
from typing import Tuple, List, Dict, Any

if "../.." not in sys.path:
    sys.path.append("../..")

if "." not in sys.path:
    sys.path.append(".")

from vidur.entities import Request

from vidur.entities import Batch, BatchStage, ExecutionTime
from vidur.execution_time_predictor import BaseExecutionTimePredictor
from vidur.execution_time_predictor import ExecutionTimePredictorRegistry
from vidur.config import SimulationConfig

from plot_utils import crop_margins

import json
import os

import dataclasses

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 9
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

class PrefillCapacityProfiler:
    def __init__(
        self, 
    ) -> None:
        # 构造预测器 (来自 base_global_scheduler.py)
        config: SimulationConfig = SimulationConfig.create_from_cli_args()
        self.execution_time_predictor: BaseExecutionTimePredictor = ExecutionTimePredictorRegistry.get(
            config.execution_time_predictor_config.get_type(),
            predictor_config=config.execution_time_predictor_config,
            replica_config=config.cluster_config.replica_config,
            replica_scheduler_config=config.cluster_config.replica_scheduler_config,
            metrics_config=config.metrics_config,
        )

    def build_batches(self) -> List[Batch]:
        # 这 4 个 Batch 分别是纯 decoding, chunk size 为 256, chunk size 为 512, chunk size 为 1024
        batch_size = 16

        decoding_request = Request(
            arrived_at=0,
            num_prefill_tokens=3000,
            num_decode_tokens=256,
            num_processed_tokens=3000,
        )
        decoding_request._is_prefill_complete = True
        half_prefill_request = Request(
            arrived_at=0,
            num_prefill_tokens=3000,
            num_decode_tokens=256,
            num_processed_tokens=1500,
        )
        prefill_request = Request(
            arrived_at=0,
            num_prefill_tokens=3000,
            num_decode_tokens=256,
            num_processed_tokens=0,
        )

        # 0. 纯 prefill
        batch0 = Batch(
            replica_id=0,
            requests=[prefill_request],
            num_tokens=[3000],
        )
        # 1. chunk size 为 256
        batch1 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size-1)] + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size-1)] + [256 - (batch_size - 1)],
        )
        # 2. chunk size 为 512
        batch2 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size-1)] + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size-1)] + [512- (batch_size - 1)],
        )
        # 3. chunk size 为 1024
        batch3 = Batch(
            replica_id=0,
            requests=[decoding_request for i in range(batch_size-1)] + [half_prefill_request],
            num_tokens=[1 for i in range(batch_size-1)] + [1024- (batch_size - 1)],
        )

        return [batch0, batch1, batch2, batch3]

    def fetch_execution_time(self, batches: List[Batch]) -> List[ExecutionTime]:
        # 使用时间预测器预测 Batch
        return [
            self.execution_time_predictor.get_execution_time(batch, 0)
            for batch in batches
        ]

    def fetch_result(self)->list[float]:
        # 使用时间预测器预测 Batch
        batches = self.build_batches()
        execution_times = self.fetch_execution_time(batches)
        prefill_capacities = [ b.num_tokens[-1]/float(t.total_time) for b, t in zip(batches, execution_times)]
        return prefill_capacities


class PrefillCapacityPlotter:

    def __init__(self, raw_prefill_capacities):
        self.raw_prefill_capacities = raw_prefill_capacities
        self.instance_names = [
            "p4d4",
            "p5d3",
            "p6d2",
            "cp512",
            "cp1k",
        ]
        self.prefill_capacities = [raw_prefill_capacities[0] * i for i in range(4, 7)]
        self.prefill_capacities += [raw_prefill_capacities[i] * 8 for i in range(2, 4)]
        print(list(zip(self.instance_names, self.prefill_capacities)))
        self._set_styles()

    def _set_styles(self):
        plt.rcParams.update({"font.size": 8})

    def plot(self):
        # 对于每个实例，绘制prefill_capacity 柱状图
        fig, ax = plt.subplots(figsize=(2.8, 2))
        bar_width = 0.35
        index = np.arange(len(self.instance_names))

        k_prefill_capacities = [x/1000 for x in self.prefill_capacities]

        ax.bar(
            index,
            k_prefill_capacities,
            bar_width,
            label="Prefill Capacity",
            color="tab:blue",
            edgecolor='black',
            linewidth=0.5,
        )
        ax.set_xlabel("Instance Types")
        ax.set_ylabel("Prefill Capacity (k token/s)")
        ax.set_xticks(index)
        ax.set_xticklabels([name.upper() for name in self.instance_names])
        plt.tight_layout()

        ax.set_ylim(0, 40)
        ax.yaxis.set_major_locator(MultipleLocator(10))
        ax.yaxis.set_minor_locator(AutoMinorLocator(5))

        plt.grid(axis='y', color='lightgray', linestyle='--')
        ax.set_axisbelow(True)

        current_dir = os.path.dirname(os.path.abspath(__file__))
        save_path = os.path.join(
            current_dir, ".cache", "figures", "small_prefill_capacity.pdf"
        )
        plt.savefig(
            save_path,
            dpi=300,
            bbox_inches="tight",
        )
        print(f"Saving figure to {save_path}")
        # plt.show()

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")


if __name__ == "__main__":
    if os.getcwd().endswith("basic"):
        current_dir = os.getcwd()
        os.chdir("../..")
    else:
        current_dir = os.getcwd()
        assert current_dir.endswith(
            "vidur"
        ), "Current directory should be project root or 'basic'"

    sys.argv = [
        "notebook",
        "--replica_config_device",
        "a100",
        "--replica_config_network_device",
        "a100_dgx",
        "--replica_config_model_name",
        "meta-llama/Llama-2-70b-hf",
        "--cluster_config_num_replicas",
        "4",
        "--replica_config_tensor_parallel_size",
        "4",
        "--replica_config_num_pipeline_stages",
        "1",
        "--request_generator_config_type",
        "synthetic",
        "--length_generator_config_type",
        "trace",
        "--interval_generator_config_type",
        "poisson",
        "--trace_request_length_generator_config_max_tokens",
        "4096",
        "--trace_request_length_generator_config_trace_file",
        "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv",
        "--synthetic_request_generator_config_num_requests",
        "1280",
        "--poisson_request_interval_generator_config_qps",
        "10",
        "--replica_scheduler_config_type",
        "sarathi",
        "--sarathi_scheduler_config_batch_size_cap",
        "256",
        "--sarathi_scheduler_config_chunk_size",
        "1024",
    ]

    prefill_capacities = PrefillCapacityProfiler().fetch_result()
    PrefillCapacityPlotter(prefill_capacities).plot()
