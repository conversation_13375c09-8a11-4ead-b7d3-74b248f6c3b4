{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from simulation_utils import load_evaluation_conf, ScriptGenerator, EvaluationRunner"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded config from /home/<USER>/Playground/customized_vidur/evaluations/basic/conf.yaml\n"]}], "source": ["conf = load_evaluation_conf(\"conf.yaml\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["commands = ScriptGenerator(conf).generate_all_commands()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:26 trace_request_length_generator.py:78] Loaded request length trace file ./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv with 28257 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:29 simulator.py:60] Starting simulation with cluster: Cluster({'id': 0, 'num_replicas': 8}) and 1280 requests\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.5580379093428s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.50943832532295s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.77710817308764s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.58896491735544s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.53186890256285s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.6714369387225s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 315.20460490157393s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:46 simulator.py:80] Simulation ended at: 311.6260773269706s\n", "INFO 04-02 10:00:46 simulator.py:83] Writing output\n", "INFO 04-02 10:00:47 simulator.py:80] Simulation ended at: 341.40141626950776s\n", "INFO 04-02 10:00:47 simulator.py:83] Writing output\n", "INFO 04-02 10:00:48 simulator.py:80] Simulation ended at: 378.0300462069815s\n", "INFO 04-02 10:00:48 simulator.py:83] Writing output\n", "INFO 04-02 10:00:52 simulator.py:80] Simulation ended at: 435.08914963209713s\n", "INFO 04-02 10:00:52 simulator.py:83] Writing output\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:40 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:41 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:41 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:41 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:42 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:43 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:45 simulator.py:95] Chrome event trace written\n", "INFO 04-02 10:01:47 simulator.py:86] Metrics written\n", "INFO 04-02 10:01:49 simulator.py:95] Chrome event trace written\n"]}], "source": ["EvaluationRunner(commands).run()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}