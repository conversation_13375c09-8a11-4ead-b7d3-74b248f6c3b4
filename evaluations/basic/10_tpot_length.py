# %%
# 引入模块
import sys
import json
import os
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoMinorLocator

from typing import Any

from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

class DataFetcher:
    def __init__(
        self, scheduler: str, qps: float, file_name: str, columns: list[str]
    ) -> None:
        dir = os.path.join(os.path.dirname(__file__), ".cache", scheduler, f"{qps:.2f}")
        self.file_path = os.path.join(dir, self._get_latest_dir(dir), file_name)
        self.columns = columns

    def fetch(self) -> tuple[list[float], list[float], list[int]]:
        data = pd.read_csv(self.file_path)
        data = data[self.columns]
        data = data.dropna()
        
        data = data.sort_values(by=self.columns[0])
        data = data.reset_index(drop=True)
        
        avgs = data.groupby(data.index // (len(data) // 5)).mean()
        avgs_list = avgs.iloc[:, 1].astype(int).tolist()
        
        # Generate CDF for column 0
        data['cdf'] = data[self.columns[0]].rank(method='max', pct=True)
        
        x = data[self.columns[0]].values.tolist()
        y = data['cdf'].values.tolist()
        return x, y, avgs_list

    def _get_latest_dir(self, dir: str) -> str:
        dirs = os.listdir(dir)
        dirs.sort()
        return dirs[-1]


class CDFPlotter:
    def __init__(self, data: tuple[list[float],list[float], list[int]], slo: float,  file_name: str) -> None:
        self.x = data[0]
        self.y = data[1]
        self.texts = data[2]
        self.slo = slo
        self.file_name = file_name

    def plot(self) -> None:
        fig, ax = plt.subplots(figsize=(3.3, 2))
        plt.rcParams.update({'font.size': 10})
        print(f'{len(self.x)=}')
        print(f'{self.texts=}')
        plt.plot(self.x, self.y, color="tab:blue")
        plt.xlabel("TPOT (s)")
        plt.ylabel("CDF")

        plt.xlim(0, max(max(self.x)*1.1, self.slo * 1.1))
        plt.ylim(0, 1.00)
        plt.xlim(0, 0.4)
        
        # y 轴 tick 间隔为 0.2
        plt.yticks(np.arange(0, 1.01, 0.20))
        
        # # 在每个 0.2 区间对应点的中间位置添加文本, 文本内容是self.texts 对应的数值
        # for i, text in enumerate(self.texts):
        #     x_pos = self.x[int((i + 0.5) * (len(self.x) // len(self.texts)))]
        #     plt.text(x_pos - 0.02, 0.2 * (i + 0.5), str(text), 
        #      fontsize=8, ha='center', va='center', color='blue')

        ####### Method 2 ##########
        # 20%
        ax.annotate('', xy=(0.12, 0.1), xytext=(0.135, 0.1),
            arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
            annotation_clip=False)
        plt.text(0.135, 0.07, f'Avg len: {str(self.texts[0])}', fontsize=9)

        # 40%
        ax.annotate('', xy=(0.16, 0.3), xytext=(0.175, 0.3),
            arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
            annotation_clip=False)
        plt.text(0.175, 0.27, f'Avg len: {str(self.texts[1])}', fontsize=9)
        plt.axhline(y=0.2, xmin=0, xmax=0.16/0.4, color='lightgray', linestyle='--', lw=0.8)

        # 60%
        ax.annotate('', xy=(0.21, 0.5), xytext=(0.225, 0.5),
            arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
            annotation_clip=False)
        plt.text(0.225, 0.47, f'Avg len: {str(self.texts[2])}', fontsize=9)
        plt.axhline(y=0.4, xmin=0, xmax=0.21/0.4, color='lightgray', linestyle='--', lw=0.8)

        # 80%
        ax.annotate('', xy=(0.24, 0.7), xytext=(0.255, 0.7),
            arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
            annotation_clip=False)
        plt.text(0.255, 0.67, f'Avg len: {str(self.texts[3])}', fontsize=9)
        plt.axhline(y=0.6, xmin=0, xmax=0.24/0.4, color='lightgray', linestyle='--', lw=0.8)

        # 100%
        ax.annotate('', xy=(0.25, 0.9), xytext=(0.266, 0.9),
            arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
            annotation_clip=False)
        plt.text(0.265, 0.87, f'Avg len: {str(self.texts[4])}', fontsize=9)
        plt.axhline(y=0.8, xmin=0, xmax=0.25/0.4, color='lightgray', linestyle='--', lw=0.8)

        plt.annotate("SLO",
                     xy=(0.1, 1.0),  # 注释的坐标位置
                     xytext=(0.02, 0.8),  # 文本的显示位置
                     arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))

        # 添加 SLO 线
        plt.axvline(self.slo, color='red', linestyle='--')
        plt.tight_layout()
        
        # plt.grid(axis='y', color='black', linestyle='--', lw=0.5)
        save_path = os.path.join(".cache", "figures", f"{self.file_name}.pdf")
        plt.savefig(save_path)
        plt.show()

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

if __name__ == "__main__":
    # 获取数据
    inputs = [
        (("cp1024", 12, "request_metrics.csv", ["decode_time_execution_plus_preemption_normalized", "request_num_decode_tokens"]), 0.100, "tpot_length"),
    ]
    
    plot_inputs = [
        (DataFetcher(*fetch_input).fetch(), slo, file_name) for (fetch_input, slo, file_name) in inputs
    ]
    
    print(plot_inputs)
    
    # 画图
    for data, slo, file_name in plot_inputs:
        plotter = CDFPlotter(data, slo, file_name)
        plotter.plot()
    
    

# %%
