# %%
import os
from typing import List, Dict, Any, Tuple, Optional
import matplotlib.pyplot as plt
from simulation_utils import (
    EvaluationConf,
    load_evaluation_conf,
    PlotterBase,
    BackendStyle,
)
import numpy as np

from matplotlib.ticker import AutoMinorLocator

from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


class TpotInterferencePlotter(PlotterBase):
    """描述 TPOT 和 干扰强度的关系的画图工具"""

    def __init__(
        self,
        conf: EvaluationConf,
        selected_plot_scheduler: str,
        backend_style: BackendStyle,
        plot_name: str,
        qps: str,
    ):
        super().__init__(conf)
        self.selected_plot_schedulers = selected_plot_scheduler
        self.backend_style = backend_style
        self.plot_name = plot_name
        self.qps = qps

    def plot(self):
        self._plot_tpot_interference()

    def _plot_tpot_interference(self):
        fig, ax = plt.subplots(figsize=(2.8, 2))
        plt.rcParams.update({"font.size": 12})

        interference, tpot = self._fetch_tpot_interference_data(
            self.selected_plot_schedulers, self.qps
        )

        # Scatter plot
        plt.scatter(
            interference,
            tpot,
            color='lightgray',
        )

        coefficients = np.polyfit(interference, tpot, 1)
        polynomial = np.poly1d(coefficients)
        fitted_values = polynomial(interference)

        # Plot the fitted curve
        plt.plot(interference, fitted_values, label="Fitted line", color="tab:blue")

        # Calculate R-squared value
        residuals = tpot - fitted_values
        ss_res = np.sum(residuals**2)
        ss_tot = np.sum((tpot - np.mean(tpot)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)

        # Add curve formula and R-squared value to the plot
        formula = f"y = {coefficients[0]:.6f}x + {coefficients[1]:.6f}"
        plt.text(
            # 0.95,
            # 0.05,
            0.04,
            0.68,
            f"{formula}\n$R^2$ = {r_squared:.2f}",
            transform=plt.gca().transAxes,
            fontsize=9,
            verticalalignment="bottom",
            horizontalalignment="left",
        )

        # 画出 y=0.1和拟合线的交点,并且在交点处向x 轴画一条线(都是用虚线)
        y_intercept = 0.1
        x_intercept = (y_intercept - coefficients[1]) / coefficients[0]
        plt.axhline(
            y=y_intercept,
            color="black",
            linestyle=":",
            xmax=x_intercept / max(interference),
        )
        plt.axvline(
            x=x_intercept,
            color="black",
            linestyle=":",
            ymax=y_intercept / max(tpot) / 1.1,
        )
        plt.annotate(
            f"{x_intercept:.2f}",
            xy=(x_intercept, 0),
            xytext=(240, 0.03),
            arrowprops=dict(facecolor="black", arrowstyle="->", color="black"),
            fontsize=8,
            color="black",
            horizontalalignment="right",
        )

        # 调整坐标轴
        plt.ylim(0, 0.3)
        plt.xlim(0, 1000)

        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))

        plt.grid(axis='x', color='lightgray', linestyle='--')
        plt.grid(axis='y', color='lightgray', linestyle='--')

        # Add labels and legend
        plt.xlabel("Interference Intensity")
        plt.ylabel("TPOT (s)")
        plt.legend(loc="lower right", frameon=False)  # 图例在左上角
        plt.tight_layout()

        save_path = os.path.join(self.figure_dir, f"small_{self.plot_name}.pdf")
        plt.savefig(save_path, dpi=300)
        print(f"Figure saved to {save_path}")

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

        # plt.show()


if __name__ == "__main__":
    conf = load_evaluation_conf("conf.yaml")
        

    selected_plot_scheduler = "cp1024"
    backend_style = BackendStyle(label="PD Agg.", marker="s", color="tab:green", zorder=1)
    
    TpotInterferencePlotter(
        conf, selected_plot_scheduler, backend_style, "tpot_interference", "12.00"
    ).plot()
