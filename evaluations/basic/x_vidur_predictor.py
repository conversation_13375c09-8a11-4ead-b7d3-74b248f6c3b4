# %%
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator
from typing import Tuple, List, Dict, Any

if "../.." not in sys.path:
    sys.path.append("../..")

if "." not in sys.path:
    sys.path.append(".")

from vidur.entities import Request

from vidur.entities import Batch, BatchStage, ExecutionTime
from vidur.execution_time_predictor import BaseExecutionTimePredictor
from vidur.execution_time_predictor import ExecutionTimePredictorRegistry
from vidur.config import SimulationConfig

from plot_utils import crop_margins

import json
import os

import dataclasses


class BatchTimePredictor:
    def __init__(
        self,
    ) -> None:
        # 构造预测器 (来自 base_global_scheduler.py)
        config: SimulationConfig = SimulationConfig.create_from_cli_args()
        self.execution_time_predictor: BaseExecutionTimePredictor = (
            ExecutionTimePredictorRegistry.get(
                config.execution_time_predictor_config.get_type(),
                predictor_config=config.execution_time_predictor_config,
                replica_config=config.cluster_config.replica_config,
                replica_scheduler_config=config.cluster_config.replica_scheduler_config,
                metrics_config=config.metrics_config,
            )
        )

    def build_batches(self) -> List[Batch]:
        # 这 4 个 Batch 分别是纯 decoding, chunk size 为 256, chunk size 为 512, chunk size 为 1024
        
        # 配置
        batch_size: int = 120
        num_prefill_tokens: int = 1024
        num_decode_tokens: int = 256

        # 基础请求
        decoding_request = Request(
            arrived_at=0,
            num_prefill_tokens=num_prefill_tokens,
            num_decode_tokens=num_decode_tokens,
            num_processed_tokens=num_prefill_tokens,
        )
        decoding_request._is_prefill_complete = True
        prefill_request = Request(
            arrived_at=0,
            num_prefill_tokens=num_prefill_tokens,
            num_decode_tokens=num_decode_tokens,
            num_processed_tokens=0,
        )
        half_prefill_request = Request(
            arrived_at=0,
            num_prefill_tokens=num_prefill_tokens,
            num_decode_tokens=num_decode_tokens,
            num_processed_tokens=num_prefill_tokens // 2,
        )

        chunk_sizes: list[None|int] = [None, 256, 512, 1024]

        batches: list[Batch] = []
        for chunk_size in chunk_sizes:
            if chunk_size is None:
                requests = [decoding_request for i in range(batch_size)]
                num_tokens = [1 for i in range(batch_size)]
            else:
                requests = [decoding_request for i in range(batch_size - 1)] + [
                    prefill_request
                ]
                num_tokens = [1 for i in range(batch_size - 1)] + [chunk_size - (batch_size - 1)]
            batches.append(Batch(replica_id=0, requests=requests, num_tokens=num_tokens))

        return batches

    def fetch_execution_time(self, batches: List[Batch]) -> List[ExecutionTime]:
        # 使用时间预测器预测 Batch
        return [
            self.execution_time_predictor.get_execution_time(batch, 0)
            for batch in batches
        ]

    def fetch_result(self) -> list[float]:
        # 使用时间预测器预测 Batch
        batches = self.build_batches()
        execution_times = self.fetch_execution_time(batches)
        return [float(t.total_time) for t in execution_times]


if __name__ == "__main__":
    if os.getcwd().endswith("basic"):
        current_dir = os.getcwd()
        os.chdir("../..")
    else:
        current_dir = os.getcwd()
        assert current_dir.endswith(
            "vidur"
        ), "Current directory should be project root or 'basic'"

    sys.argv = [
        "notebook",
        "--replica_config_device",
        "a100",
        "--replica_config_network_device",
        "a100_dgx",
        "--replica_config_model_name",
        "Qwen/Qwen2.5-14B",
        "--cluster_config_num_replicas",
        "1",
        "--replica_config_tensor_parallel_size",
        "1",
        "--replica_config_num_pipeline_stages",
        "1",
        "--request_generator_config_type",
        "synthetic",
        "--length_generator_config_type",
        "trace",
        "--interval_generator_config_type",
        "poisson",
        "--trace_request_length_generator_config_max_tokens",
        "16384",
        "--trace_request_length_generator_config_trace_file",
        "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv",
        "--synthetic_request_generator_config_num_requests",
        "1280",
        "--poisson_request_interval_generator_config_qps",
        "10",
        "--replica_scheduler_config_type",
        "sarathi",
        "--sarathi_scheduler_config_batch_size_cap",
        "256",
        "--sarathi_scheduler_config_chunk_size",
        "1024",
    ]

    results = BatchTimePredictor().fetch_result()
    print(results)
