from math import ceil

from vidur.entities.batch import Batch, Request
from vidur.scheduler.replica_scheduler.base_replica_scheduler import (
    BaseReplicaScheduler,
)


class TriangleReplicaScheduler(BaseReplicaScheduler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 内存管理: 最大 batch size 和 内存水位线
        self._max_micro_batch_size = self._config.batch_size_cap // self._num_stages
        self._watermark_blocks = int(
            self._config.watermark_blocks_fraction * self._config.num_blocks
        )

        # 请求队列
        self._prefill_queue = []
        self._decode_queue = []
        self._preempted_decode_requests = []
        self._preempted_prefill_requests = []
        self._restarted_requests = []
        self._evicted_requests = []

        # chunked prefill 配置
        self._num_running_batches = 0
        self._adaptive_chunk_size = self._config.chunk_size
        self._chunk_size = self._config.chunk_size

        # stat
        self._num_processed_prefill_requests = 0
        self._num_processed_prefill_tokens = 0

    def _set_mode_aware_chunk_size(self) -> None:
        if self._preempted_decode_requests or self._decode_queue:  # PD 混合模式
            self._chunk_size = self._adaptive_chunk_size
        else:  # 纯 Prefill 模式
            self._chunk_size = 4096

    def adjust_watermark_blocks(self, watermark_blocks_fraction: int) -> None:
        self._watermark_blocks = int(
            watermark_blocks_fraction * self._config.num_blocks
        )

    def adjust_adaptive_chunk_size(self, chunk_size: int) -> None:
        self._adaptive_chunk_size = chunk_size

    def get_prefill_pressure(self) -> None:
        # 返回值: (剩余任务量, 队列中任务量)
        # 剩余任务量 = preempted 任务量加 Stage 中的任务量
        preempted_tokens = sum(
            [
                req.num_prefill_tokens - req.num_processed_tokens
                for req in self._preempted_prefill_requests
            ]
        )
        for stage_scheduler in self._replica_stage_schedulers.values():
            preempted_tokens += stage_scheduler.get_prefill_tokens()
        queueing_tokens = sum([req.num_prefill_tokens for req in self._prefill_queue])
        return (preempted_tokens, queueing_tokens)

    def set_global_scheduler(self, global_scheduler) -> None:
        self._global_scheduler = global_scheduler

    def _get_next_batch(self) -> Batch:
        # 所有任务包括: 运行中 Decode, 运行中 Prefill, 新 Decode, 新 Prefill.
        # 内存使用情况: 运行中任务占用内存, 新任务需要大量内存分配.
        # * 入 batch 优先级: 新 Decode, 运行中 Decode, 运行中 Prefill, 新 Prefill.
        # * HBM 踢出策略: 优先踢出最老的 运行中 Decode.

        self._set_mode_aware_chunk_size()

        requests = []
        num_tokens = []
        num_batch_tokens = 0
        contain_decode = False

        # 按处理过的 decode tokens 数量排序 (左新右旧)
        self._preempted_decode_requests.sort(
            key=lambda req: req.num_processed_decode_tokens
        )

        # 新 decode 尽可能加入 batch
        while self._decode_queue:
            if len(requests) == self._max_micro_batch_size:
                break

            if not self._evict_to_allocate(self._decode_queue[0]):
                break

            next_num_tokens = self._get_request_next_num_tokens(
                self._decode_queue[0], num_batch_tokens
            )
            assert next_num_tokens == 1

            request = self._decode_queue.pop(0)
            self._allocate_request(request)
            requests.append(request)
            num_tokens.append(next_num_tokens)
            num_batch_tokens += next_num_tokens
            contain_decode = True

        # 运行中 decode 全数加入 batch
        while self._preempted_decode_requests:
            if len(requests) == self._max_micro_batch_size:
                break

            next_num_tokens = self._get_request_next_num_tokens(
                self._preempted_decode_requests[0], num_batch_tokens
            )
            assert next_num_tokens == 1

            if not self._evict_to_allocate(self._preempted_decode_requests[0]):
                assert False, "踢出自己, 肯定能容纳"

            if not self._preempted_decode_requests:  # 自己被踢出
                break

            request = self._preempted_decode_requests.pop(0)
            self._allocate_request(request)
            requests.append(request)
            num_tokens.append(next_num_tokens)
            num_batch_tokens += next_num_tokens
            contain_decode = True

        # 运行中 Prefill 尽可能加入 batch
        while self._preempted_prefill_requests:
            if len(requests) == self._max_micro_batch_size:
                break

            next_num_tokens = self._get_request_next_num_tokens(
                self._preempted_prefill_requests[0], num_batch_tokens
            )

            if next_num_tokens == 0:
                break

            request = self._preempted_prefill_requests.pop(0)
            self._allocate_request(request)  # 冗余
            requests.append(request)
            num_tokens.append(next_num_tokens)
            num_batch_tokens += next_num_tokens
            assert not request.is_prefill_complete

        # load-aware 调度
        if num_batch_tokens < self._chunk_size:
            self._global_scheduler.reschedule_prefill_requests(
                self._replica_id, contain_decode
            )

        # 新 Prefill 尽可能加入 batch
        while self._prefill_queue:

            if len(self._allocation_map) == self._config.batch_size_cap:
                break

            if len(requests) == self._max_micro_batch_size:
                break

            next_num_tokens = self._get_request_next_num_tokens(
                self._prefill_queue[0], num_batch_tokens
            )

            if next_num_tokens == 0:
                break

            while not self._can_allocate_request(self._prefill_queue[0]):
                # 从 requests 中从后向前移除已分配的 decode 请求
                if self._replica_id == 0 or not requests:
                    break
                for i in range(len(requests) - 1, -1, -1):
                    if requests[i].is_prefill_complete:
                        self.free(requests[i].id)
                        self._evicted_requests.append(requests[i])
                        assert num_tokens[i] == 1
                        del requests[i]
                        del num_tokens[i]
                        num_batch_tokens -= 1
                        break
            if self._can_allocate_request(self._prefill_queue[0]):
                next_num_tokens = self._get_request_next_num_tokens(
                    self._prefill_queue[0], num_batch_tokens
                )
                request = self._prefill_queue.pop(0)
                self._allocate_request(request)
                requests.append(request)
                num_tokens.append(next_num_tokens)
                num_batch_tokens += next_num_tokens

        if not requests:
            return

        while self._preempted_decode_requests:
            self._evict_preempted_decode()

        return Batch(self._replica_id, requests, num_tokens)

    def on_batch_end(self, batch: Batch) -> None:
        self._num_running_batches -= 1

        disaggregated_requests = self._restarted_requests + self._evicted_requests
        self._restarted_requests = []
        self._evicted_requests = []

        for request in batch.requests:
            if request.completed:
                self.free(request.id)
            elif request.is_prefill_complete and not request.has_started_decode:
                self.free(request.id)
                disaggregated_requests.append(request)  # * PD 分离
                # log
                self._num_processed_prefill_requests += 1
                self._num_processed_prefill_tokens += request.num_prefill_tokens
                # print(
                #     f" Replica {self._replica_id} 完成 {self._num_processed_prefill_requests} Prefill, {self._num_processed_prefill_tokens} tokens"
                # )
            else:
                if request.has_started_decode:
                    self._preempted_decode_requests.append(request)
                else:
                    assert not request.is_prefill_complete
                    self._preempted_prefill_requests.append(request)

        return disaggregated_requests

    def add_request(self, request: Request) -> None:
        # 重写
        # 分离 Prefill 和 Decode 请求
        if request is None:
            return

        request._replica_id = self._replica_id
        if request.is_prefill_complete:
            self._decode_queue.append(request)
        else:
            self._prefill_queue.append(request)

    def _can_allocate_request(self, request: Request) -> bool:
        if request.id not in self._allocation_map:
            # new request
            num_required_blocks = ceil(
                request.num_prefill_tokens / self._config.block_size
            )
            return (
                self._config.num_blocks
                - self._num_allocated_blocks
                - num_required_blocks
                >= self._watermark_blocks
            )

        # vllm requires at least one block to be available
        return self._config.num_blocks - self._num_allocated_blocks >= 1

    def _allocate_request(self, request: Request) -> None:
        if request.id not in self._allocation_map:
            # new request (decode: processed tokens, prefill: prefill tokens)
            num_required_blocks = ceil(
                max(request.num_processed_tokens + 1, request.num_prefill_tokens + 1)
                / self._config.block_size
            )
            self.allocate(request.id, num_required_blocks)
            return

        num_tokens_reserved = self._allocation_map[request.id] * self._config.block_size
        num_tokens_required = max(0, request.num_processed_tokens - num_tokens_reserved)

        assert (
            num_tokens_required == 0 or num_tokens_required == 1
        ), f"num_tokens_required: {num_tokens_required}"

        if num_tokens_required == 0:
            return

        self.allocate(request.id, 1)

    def _get_request_next_num_tokens(
        self, request: Request, num_batch_tokens: int
    ) -> int:
        assert not request.completed

        if request.is_prefill_complete:  # decode
            return 1
        else:  # prefill
            next_num_tokens = min(
                request.num_prefill_tokens - request.num_processed_tokens,
                self._chunk_size - num_batch_tokens,
            )
            next_num_tokens = max(0, next_num_tokens)
            return next_num_tokens

    def _evict_to_allocate(self, request) -> bool:
        # 踢出最老的 运行中 Decode, 直到能够分配 request
        while not self._can_allocate_request(request):
            if not self._preempted_decode_requests:
                return False
            if self._replica_id > 0:  # 非首节点踢出请求
                self._evict_preempted_decode()
            else:  # 首节点重启请求 (因为无处可踢)
                self._restart_request(request)
        return True

    def _restart_request(self, request: Request) -> None:
        request.restart()
        self.free(request.id)
        self._restarted_requests.append(request)

    def _evict_preempted_decode(self) -> None:
        if not self._preempted_decode_requests:
            return
        self._preempted_decode_requests.sort(
            key=lambda req: req.num_processed_decode_tokens
        )
        request = self._preempted_decode_requests.pop(-1)
        self.free(request.id)
        self._evicted_requests.append(request)
        # log
        # print(f"Replica {self._replica_id} 踢出 Decode 请求 {request.id}")
