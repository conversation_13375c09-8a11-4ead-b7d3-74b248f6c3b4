from vidur.scheduler.global_scheduler.lor_global_scheduler import LORGlobalScheduler
from vidur.scheduler.global_scheduler.random_global_scheduler import (
    RandomGlobalScheduler,
)
from vidur.scheduler.global_scheduler.round_robin_global_scheduler import (
    RoundRobinGlobalScheduler,
)
from vidur.scheduler.global_scheduler.disaggregation_global_scheduler import (
    DisaggregationGlobalScheduler,
)
from vidur.scheduler.global_scheduler.hybrid_global_scheduler import (
    HybridGlobalScheduler,
)
from vidur.scheduler.global_scheduler.triangle_global_scheduler import (
    TriangleGlobalScheduler,
)
from vidur.types import GlobalSchedulerType
from vidur.utils.base_registry import BaseRegistry


class GlobalSchedulerRegistry(BaseRegistry):
    @classmethod
    def get_key_from_str(cls, key_str: str) -> GlobalSchedulerType:
        return GlobalSchedulerType.from_str(key_str)


GlobalSchedulerRegistry.register(GlobalSchedulerType.RANDOM, RandomGlobalScheduler)
GlobalSchedulerRegistry.register(
    GlobalSchedulerType.ROUND_ROBIN, RoundRobinGlobalScheduler
)
GlobalSchedulerRegistry.register(GlobalSchedulerType.LOR, LORGlobalScheduler)
GlobalSchedulerRegistry.register(
    GlobalSchedulerType.DISAGGREGATION, DisaggregationGlobalScheduler
)
GlobalSchedulerRegistry.register(GlobalSchedulerType.HYBRID, HybridGlobalScheduler)
GlobalSchedulerRegistry.register(GlobalSchedulerType.TRIANGLE, TriangleGlobalScheduler)
