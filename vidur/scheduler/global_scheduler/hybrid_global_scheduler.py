from typing import List, Tuple

from vidur.entities import Request
from vidur.scheduler.global_scheduler.base_global_scheduler import BaseGlobalScheduler


class HybridGlobalScheduler(BaseGlobalScheduler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._prefill_request_queue: List[Request] = []
        self._decode_request_queue: List[Request] = []

        self._prefill_request_counter = 0
        self._decode_request_counter = 0

        # 参数
        (
            self._replica_split_ratio,
            self._replica_mixed_prefill_factor,
            self._replica_mixed_decode_factor,
            self._policy,
        ) = (
            self._config.cluster_config.global_scheduler_config.replica_split_ratio,
            self._config.cluster_config.global_scheduler_config.replica_mixed_prefill_factor,
            self._config.cluster_config.global_scheduler_config.replica_mixed_decode_factor,
            self._config.cluster_config.global_scheduler_config.policy,
        )

        # 节点分配
        self._num_prefill_replicas = round(
            self._num_replicas * self._replica_split_ratio
        )
        self._num_decode_replicas = self._num_replicas - self._num_prefill_replicas

        # 请求分配计划
        self._prefill_intensity = [1.0] * self._num_prefill_replicas + [
            self._replica_mixed_prefill_factor
        ] * self._num_decode_replicas

        self._decode_intensity = [
            self._replica_mixed_decode_factor
        ] * self._num_prefill_replicas + [1.0] * self._num_decode_replicas

        for id in range(self._num_prefill_replicas):
            self.get_replica_scheduler(id).set_prefill_replica()

        for id in range(self._num_prefill_replicas, self._num_replicas):
            self.get_replica_scheduler(id).set_decode_replica()

        assert self._num_prefill_replicas > 0 and self._num_decode_replicas > 0
        assert (
            self._num_prefill_replicas + self._num_decode_replicas == self._num_replicas
        )
        assert self._num_replicas > 1

        # for policy "length_aware_round_robin"
        self._replica_prefill_loads = [1] * self._num_replicas
        self._replica_decode_loads = [1] * self._num_replicas

    def add_request(self, request: Request) -> None:
        request._is_disaggregated = True
        if not request.is_prefill_complete:
            self._prefill_request_queue.append(request)
        else:
            self._decode_request_queue.append(request)

    def sort_requests(self) -> None:
        self._prefill_request_queue.sort(key=lambda request: request._arrived_at)
        self._decode_request_queue.sort(key=lambda request: request._arrived_at)

    def schedule(self) -> List[Tuple[int, Request]]:
        self.sort_requests()

        if self._policy == "length_aware_round_robin":
            return self._length_aware_round_robin()
        elif self._policy == "round_robin":
            return self._round_robin()

    def _length_aware_round_robin(self) -> List[Tuple[int, Request]]:

        request_mapping = []
        while self._prefill_request_queue:
            request = self._prefill_request_queue.pop(0)
            replica_id = self._weighted_round_robin(
                self._replica_prefill_loads,
                1,
                self._prefill_intensity,
                0,
            )
            request_mapping.append((replica_id, request))

        while self._decode_request_queue:
            request = self._decode_request_queue.pop(0)
            replica_id = self._weighted_round_robin(
                self._replica_decode_loads,
                1,
                self._decode_intensity,
                self._num_replicas - 1,
            )
            request_mapping.append((replica_id, request))

        return request_mapping

    def _round_robin(self) -> List[Tuple[int, Request]]:
        request_mapping = []
        while self._prefill_request_queue:
            request = self._prefill_request_queue.pop(0)
            replica_id = self._round_robin(self._replica_prefill_loads, 1)  # by request
            request_mapping.append((replica_id, request))

        while self._decode_request_queue:
            request = self._decode_request_queue.pop(0)
            replica_id = self._round_robin(self._replica_decode_loads, 1)
            request_mapping.append((replica_id, request))

        return request_mapping

    def _weighted_round_robin(self, loads, new_load, weights, standard) -> int:
        load_factor = [load / loads[standard] for load in loads]
        load_diff = [load - weight for load, weight in zip(load_factor, weights)]
        # find the replica with the least load diff
        replica_id = load_diff.index(min(load_diff))
        loads[replica_id] += new_load
        return replica_id

    def _round_robin(self, loads, new_load) -> int:
        replica_id = loads.index(min(loads))
        loads[replica_id] += new_load
        return replica_id
