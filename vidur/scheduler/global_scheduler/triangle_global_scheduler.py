from typing import List, Tuple

from vidur.entities import Request
from vidur.scheduler.global_scheduler.base_global_scheduler import BaseGlobalScheduler


class TriangleGlobalScheduler(BaseGlobalScheduler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._prefill_request_queue: List[Request] = []
        self._triggered_prefill_request_queue: List[Request] = []
        self._decode_request_queue: List[Request] = []

        # 传递 GS 配置到 RS
        for replica_scheduler in self._replica_schedulers.values():
            replica_scheduler.set_global_scheduler(self)
            # replica_scheduler.adjust_watermark_blocks(0.15)

        # 设定默认 chunk size
        self.set_default_chunk_size()

        self._last_replica_busy_time = 0

        # 调度依据
        self._replica_prefill_loads = [1] * (self._num_replicas - 1)

        # prefill 调度策略
        self._prefill_schedule_strategy = "load-aware"
        # self._prefill_schedule_strategy = "round-robin"
        # self._prefill_schedule_strategy = "load-aware+length-aware"

    def reschedule_prefill_requests(self, replica_id, is_hybrid_replica=False) -> None:
        if replica_id == self._num_replicas - 1:
            return

        # 回收所有 Prefill 请求
        for id in range(self._num_replicas):
            replica_scheduler = self.get_replica_scheduler(id)
            if replica_scheduler._prefill_queue:
                self._triggered_prefill_request_queue.extend(
                    replica_scheduler._prefill_queue
                )
                replica_scheduler._prefill_queue = []

        self._triggered_prefill_request_queue.sort(
            key=lambda request: request._arrived_at
        )

        if not self._triggered_prefill_request_queue:
            return

        if len(self._triggered_prefill_request_queue) > self._num_replicas - 1:
            self.set_turbo_chunk_size()
        else:
            self.set_default_chunk_size()

        replica_scheduler = self.get_replica_scheduler(replica_id)

        # ! load-aware 分配
        if self._prefill_schedule_strategy == "load-aware":
            replica_scheduler._prefill_queue.append(
                self._triggered_prefill_request_queue.pop(0)
            )
        elif self._prefill_schedule_strategy == "round-robin":
            for request in self._triggered_prefill_request_queue:
                if request._id % (self._num_replicas - 1) == replica_id:
                    replica_scheduler._prefill_queue.append(request)
                    self._triggered_prefill_request_queue.remove(request)
                    break
        elif self._prefill_schedule_strategy == "load-aware+length-aware":
            if is_hybrid_replica:
                # PD 融合节点, 考虑 length-aware
                self._triggered_prefill_request_queue.sort(
                    key=lambda request: request._num_prefill_tokens
                )
                request = self._triggered_prefill_request_queue.pop(0)
                replica_scheduler._prefill_queue.append(request)
                self._triggered_prefill_request_queue.sort(
                    key=lambda request: request._arrived_at
                )
                # log
                # print(
                #     f"PD 融合节点, 分配 Prefill 请求长度为 {request._num_prefill_tokens}"
                # )
            else:
                # 非 PD 节点, 仅考虑 load-aware
                replica_scheduler._prefill_queue.append(
                    self._triggered_prefill_request_queue.pop(0)
                )
        else:
            raise NotImplementedError

    def schedule(self) -> List[Tuple[int, Request]]:
        # 分离 Prefill 和 Decode 的全局调度策略实现
        self.sort_requests()
        schedule_map = []
        schedule_map.extend(self._schedule_prefill_requests())
        schedule_map.extend(self._schedule_decode_requests())
        return schedule_map

    def _schedule_prefill_requests(self) -> List[Tuple[int, Request]]:
        # Prefill 调度策略实现
        schedule_map = []
        for request in self._prefill_request_queue:
            replica_id = self._round_robin(self._replica_prefill_loads, 1)
            schedule_map.append((replica_id, None))
            self._triggered_prefill_request_queue.append(request)
        self._prefill_request_queue = []
        return schedule_map

    def _schedule_decode_requests(self) -> List[Tuple[int, Request]]:
        # Decode 调度策略实现
        schedule_map = []
        for request in self._decode_request_queue:
            if not request.has_started_decode:
                replica_id = self._num_replicas - 1
            else:
                replica_id = request._replica_id  # Decode 中包含上一个目的地 repilca_id
                assert replica_id > 0
                replica_id -= 1

            schedule_map.append((replica_id, request))
        self._decode_request_queue = []
        return schedule_map

    def _round_robin(self, loads, new_load) -> int:
        replica_id = loads.index(min(loads))
        loads[replica_id] += new_load
        return replica_id

    def add_request(self, request: Request) -> None:
        # 重写
        # 分离 Prefill 和 Decode 的请求
        request._is_disaggregated = True
        if not request.is_prefill_complete:
            self._prefill_request_queue.append(request)
        else:
            self._decode_request_queue.append(request)

    def sort_requests(self) -> None:
        # 重写
        self._prefill_request_queue.sort(key=lambda request: request._arrived_at)
        self._decode_request_queue.sort(key=lambda request: request._arrived_at)

    def set_default_chunk_size(self) -> None:
        self.get_replica_scheduler(0).adjust_adaptive_chunk_size(4096)
        self.get_replica_scheduler(self._num_replicas - 1).adjust_adaptive_chunk_size(
            128
        )
        # 将 1 到 self._num_replicas - 1 的节点分为 2组, 第一组设定为 512, 第二组设定为 128
        for i in range(1, self._num_replicas - 1):
            if i < (self._num_replicas - 1) // 2:
                self.get_replica_scheduler(i).adjust_adaptive_chunk_size(512)
            else:
                self.get_replica_scheduler(i).adjust_adaptive_chunk_size(128)

    def set_turbo_chunk_size(self) -> None:
        self.get_replica_scheduler(0).adjust_adaptive_chunk_size(4096)
        self.get_replica_scheduler(self._num_replicas - 1).adjust_adaptive_chunk_size(
            128
        )
        # 将 1 到 self._num_replicas - 1 的节点分为 2组, 第一组设定为 512, 第二组设定为 128
        for i in range(1, self._num_replicas - 1):
            if i < (self._num_replicas - 1) // 2:
                self.get_replica_scheduler(i).adjust_adaptive_chunk_size(512)
            else:
                self.get_replica_scheduler(i).adjust_adaptive_chunk_size(256)
