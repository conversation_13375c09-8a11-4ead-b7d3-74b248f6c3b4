from typing import List, Tuple

from vidur.entities import Request
from vidur.scheduler.global_scheduler.base_global_scheduler import BaseGlobalScheduler


class DisaggregationGlobalScheduler(BaseGlobalScheduler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._prefill_request_queue: List[Request] = []
        self._decode_request_queue: List[Request] = []

        self._prefill_request_counter = 0
        self._decode_request_counter = 0

        self._replica_split_ratio = (
            self._config.cluster_config.global_scheduler_config.replica_split_ratio
        )
        self._num_prefill_replicas = round(
            self._num_replicas * self._replica_split_ratio
        )
        self._num_decode_replicas = self._num_replicas - self._num_prefill_replicas

        for id in range(self._num_prefill_replicas):
            self.get_replica_scheduler(id).set_prefill_replica()

        for id in range(self._num_prefill_replicas, self._num_replicas):
            self.get_replica_scheduler(id).set_decode_replica()

        assert self._num_prefill_replicas > 0 and self._num_decode_replicas > 0
        assert (
            self._num_prefill_replicas + self._num_decode_replicas == self._num_replicas
        )

    def add_request(self, request: Request) -> None:
        request._is_disaggregated = True
        if not request.is_prefill_complete:
            self._prefill_request_queue.append(request)
        else:
            self._decode_request_queue.append(request)

    def sort_requests(self) -> None:
        self._prefill_request_queue.sort(key=lambda request: request._arrived_at)
        self._decode_request_queue.sort(key=lambda request: request._arrived_at)

    def schedule(self) -> List[Tuple[int, Request]]:
        self.sort_requests()

        request_mapping = []
        while self._prefill_request_queue:
            request = self._prefill_request_queue.pop(0)
            replica_id = self._prefill_request_counter % self._num_prefill_replicas
            self._prefill_request_counter += 1
            request_mapping.append((replica_id, request))
        while self._decode_request_queue:
            request = self._decode_request_queue.pop(0)
            replica_id = (
                self._decode_request_counter % self._num_decode_replicas
                + self._num_prefill_replicas
            )
            self._decode_request_counter += 1
            request_mapping.append((replica_id, request))

        return request_mapping
